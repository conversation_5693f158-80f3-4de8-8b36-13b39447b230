# Environment Variables Template
# Copy this file to .env.local for local development
# Copy this file to .env.production for production deployment

# Node Environment (development, staging, production)
NODE_ENV=development

# WebSocket URL for real-time chat functionality
# Development: ws://localhost:3001/ws/
# Production: wss://k2-api.klubworks.com/ws/
NEXT_PUBLIC_WEBSOCKET_URL=ws://localhost:3001/ws/

# Authentication Service URL
# Development: http://localhost:3002/auth-svc/api/v1
# Production: https://k2-auth.klubworks.com/auth-svc/api/v1
NEXT_PUBLIC_AUTH_SERVICE_URL=http://localhost:3002/auth-svc/api/v1

# Main Backend Service URL
# Development: http://localhost:8080/api/conversation-svc
# Production: https://k2-api.klubworks.com/api/conversation-svc
NEXT_PUBLIC_SERVICE_BASE_URL=http://localhost:8080/api/conversation-svc

# OAuth Client ID for authentication
# Development: demand-app-client-dev
# Production: demand-app-client
NEXT_PUBLIC_OAUTH_CLIENT_ID=demand-app-client-dev

# OAuth Redirect URL after authentication
# Development: http://localhost:3000/assistant
# Production: https://k2-demand.klubworks.com/assistant
NEXT_PUBLIC_OAUTH_REDIRECT_URL=http://localhost:3000/assistant

# Optional: Application Version (for health checks)
APP_VERSION=v1.0.0

# Optional: Disable Next.js telemetry
NEXT_TELEMETRY_DISABLED=1
