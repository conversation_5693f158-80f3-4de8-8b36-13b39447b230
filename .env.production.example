# Production Environment Variables Template
# Copy this file to .env.production for production deployment
# These values will be used when NODE_ENV=production

# Node Environment
NODE_ENV=production

# WebSocket URL for real-time chat functionality
# Production WebSocket server with SSL
NEXT_PUBLIC_WEBSOCKET_URL=wss://k2-api.klubworks.com/ws/

# Authentication Service URL
# Production auth service
NEXT_PUBLIC_AUTH_SERVICE_URL=https://k2-auth.klubworks.com/auth-svc/api/v1

# Main Backend Service URL
# Production conversation service
NEXT_PUBLIC_SERVICE_BASE_URL=https://k2-api.klubworks.com/api/conversation-svc

# OAuth Client ID for authentication
# Production client ID
NEXT_PUBLIC_OAUTH_CLIENT_ID=demand-app-client

# OAuth Redirect URL after authentication
# Production redirect URL
NEXT_PUBLIC_OAUTH_REDIRECT_URL=https://k2-demand.klubworks.com/assistant

# Application Version (for health checks)
APP_VERSION=v1.0.0

# Disable Next.js telemetry in production
NEXT_TELEMETRY_DISABLED=1
