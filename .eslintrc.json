{"extends": ["next/core-web-vitals", "prettier"], "plugins": ["import", "jsx-a11y"], "rules": {"react/no-unescaped-entities": "off", "import/first": "error", "import/newline-after-import": "error", "import/no-duplicates": "error", "jsx-a11y/alt-text": "warn", "jsx-a11y/aria-props": "warn", "jsx-a11y/aria-proptypes": "warn", "jsx-a11y/aria-unsupported-elements": "warn", "jsx-a11y/role-has-required-aria-props": "warn", "jsx-a11y/role-supports-aria-props": "warn", "react/prop-types": "off", "react/react-in-jsx-scope": "off", "react/display-name": "off", "no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}]}}