# Docker Deployment Guide for K2 Customer App

## Overview

This guide explains how to build and deploy the K2 Customer App using Docker while handling Next.js NEXT_PUBLIC environment variables properly across different environments.

## The Challenge

Next.js requires `NEXT_PUBLIC_*` environment variables at build time because they're embedded into the client-side JavaScript bundles. This creates a challenge when you want to:
- Build once and deploy to multiple environments
- Avoid hardcoding environment-specific values in the image
- Keep sensitive configuration out of the Docker image

## Solution: Runtime Environment Injection

We use a multi-stage Docker build with runtime environment variable injection:

1. **Build with placeholders**: During build, we use placeholder values for NEXT_PUBLIC variables
2. **Runtime injection**: An entrypoint script replaces these placeholders with actual values when the container starts

## File Structure

```
k2-customer-app/
├── Dockerfile              # Multi-stage build configuration
├── docker-entrypoint.sh    # Runtime environment injection script
├── .dockerignore          # Files to exclude from Docker build
└── docker-compose.yml     # Local development with Docker
```

## Building the Image

### Basic Build
```bash
docker build -t k2-customer-app:latest .
```

### Build with Custom Tag
```bash
docker build -t k2-customer-app:v1.0.0 .
```

### Build for Production
```bash
docker build -t k2-customer-app:production --target runner .
```

## Running the Container

### Using Docker Run

#### Development Environment
```bash
docker run -d \
  --name k2-customer-dev \
  -p 3000:3000 \
  -e NEXT_PUBLIC_API_URL=http://dev-api.example.com/api \
  -e NEXT_PUBLIC_WS_URL=ws://dev-api.example.com \
  -e NEXT_PUBLIC_ORY_URL=http://dev-auth.example.com \
  k2-customer-app:latest
```

#### Staging Environment
```bash
docker run -d \
  --name k2-customer-staging \
  -p 3000:3000 \
  -e NEXT_PUBLIC_API_URL=https://staging-api.example.com/api \
  -e NEXT_PUBLIC_WS_URL=wss://staging-api.example.com \
  -e NEXT_PUBLIC_ORY_URL=https://staging-auth.example.com \
  k2-customer-app:latest
```

#### Production Environment
```bash
docker run -d \
  --name k2-customer-prod \
  -p 3000:3000 \
  -e NEXT_PUBLIC_API_URL=https://api.example.com/api \
  -e NEXT_PUBLIC_WS_URL=wss://api.example.com \
  -e NEXT_PUBLIC_ORY_URL=https://auth.example.com \
  --restart unless-stopped \
  k2-customer-app:latest
```

### Using Docker Compose

1. Create an environment file for each environment:

```bash
# .env.development
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_WS_URL=ws://localhost:5000
NEXT_PUBLIC_ORY_URL=http://127.0.0.1:3002

# .env.staging
NEXT_PUBLIC_API_URL=https://staging-api.example.com/api
NEXT_PUBLIC_WS_URL=wss://staging-api.example.com
NEXT_PUBLIC_ORY_URL=https://staging-auth.example.com

# .env.production
NEXT_PUBLIC_API_URL=https://api.example.com/api
NEXT_PUBLIC_WS_URL=wss://api.example.com
NEXT_PUBLIC_ORY_URL=https://auth.example.com
```

2. Run with specific environment:

```bash
# Development
docker-compose --env-file .env.development up -d

# Staging
docker-compose --env-file .env.staging up -d

# Production
docker-compose --env-file .env.production up -d
```

## Kubernetes Deployment

### ConfigMap for Environment Variables

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: k2-customer-app-config
data:
  NEXT_PUBLIC_API_URL: "https://api.example.com/api"
  NEXT_PUBLIC_WS_URL: "wss://api.example.com"
  NEXT_PUBLIC_ORY_URL: "https://auth.example.com"
```

### Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: k2-customer-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: k2-customer-app
  template:
    metadata:
      labels:
        app: k2-customer-app
    spec:
      containers:
      - name: k2-customer-app
        image: your-registry/k2-customer-app:latest
        ports:
        - containerPort: 3000
        envFrom:
        - configMapRef:
            name: k2-customer-app-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## CI/CD Pipeline Example

### GitHub Actions

```yaml
name: Build and Deploy

on:
  push:
    branches: [main, develop]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Docker image
      run: docker build -t k2-customer-app:${{ github.sha }} .
    
    - name: Push to registry
      run: |
        echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker tag k2-customer-app:${{ github.sha }} ${{ secrets.DOCKER_REGISTRY }}/k2-customer-app:${{ github.sha }}
        docker push ${{ secrets.DOCKER_REGISTRY }}/k2-customer-app:${{ github.sha }}
    
    - name: Deploy to environment
      run: |
        # Deploy logic here (kubectl, helm, etc.)
```

## Environment Variables Reference

### Required Runtime Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `NEXT_PUBLIC_API_URL` | Backend API URL | `https://api.example.com/api` |
| `NEXT_PUBLIC_WS_URL` | WebSocket URL | `wss://api.example.com` |
| `NEXT_PUBLIC_ORY_URL` | Ory Auth Service URL | `https://auth.example.com` |

### Optional Runtime Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Application port | `3000` |
| `NODE_ENV` | Node environment | `production` |

## Troubleshooting

### Environment Variables Not Working

1. **Check container logs**:
```bash
docker logs k2-customer-app
```

2. **Verify environment variables inside container**:
```bash
docker exec k2-customer-app env | grep NEXT_PUBLIC
```

3. **Check if entrypoint script ran**:
```bash
docker exec k2-customer-app cat .next/static/chunks/pages/_app-*.js | grep "RUNTIME_"
```
If you see "RUNTIME_" strings, the replacement didn't work.

### Build Failures

1. **Clear Docker cache**:
```bash
docker build --no-cache -t k2-customer-app:latest .
```

2. **Check Node version compatibility**:
Ensure your local Node version matches the Docker image (Node 20).

### Performance Issues

1. **Use multi-stage build cache**:
```bash
docker build --target deps -t k2-deps:latest .
docker build --cache-from k2-deps:latest -t k2-customer-app:latest .
```

2. **Optimize image size**:
- The production image uses Alpine Linux
- Only production dependencies are included
- Static files are copied from the builder stage

## Security Best Practices

1. **Non-root user**: The container runs as the `nextjs` user, not root
2. **No secrets in image**: Environment variables are injected at runtime
3. **Minimal base image**: Alpine Linux reduces attack surface
4. **Health checks**: Built-in health check endpoint for monitoring
5. **Read-only filesystem**: Consider adding `readOnlyRootFilesystem: true` in Kubernetes

## Advanced Configuration

### Using Build Arguments for Different Environments

If you need some build-time customization:

```dockerfile
# In Dockerfile
ARG BUILD_ENV=production
ENV BUILD_ENV=$BUILD_ENV
```

```bash
# Build command
docker build --build-arg BUILD_ENV=staging -t k2-customer-app:staging .
```

### Custom Next.js Configuration

Modify `next.config.js` to support Docker:

```javascript
module.exports = {
  output: 'standalone',
  experimental: {
    outputFileTracingRoot: path.join(__dirname, '../../'),
  },
  // Other configurations...
}
```

## Monitoring and Logging

### Health Check Endpoint

The application should expose a health check endpoint:

```typescript
// app/api/health/route.ts
export async function GET() {
  return Response.json({ status: 'ok' }, { status: 200 });
}
```

### Logging

Container logs can be accessed via:
```bash
docker logs -f k2-customer-app
```

For production, consider using a centralized logging solution like ELK stack or CloudWatch.

## Conclusion

This Docker setup allows you to:
- Build once, deploy everywhere
- Keep environment-specific configuration out of the image
- Maintain security by not hardcoding secrets
- Scale horizontally with container orchestration
- Deploy consistently across all environments

The runtime environment injection approach ensures that your Next.js application can use different API endpoints and configuration without rebuilding the Docker image for each environment.