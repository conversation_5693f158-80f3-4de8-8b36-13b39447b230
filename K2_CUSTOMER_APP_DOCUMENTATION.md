# K2 Customer App Documentation

## Overview

K2 Customer App is a conversational B2B SME lending platform built by Klub that provides an AI-powered interface for businesses to access funding and financial services. The application serves as a customer-facing platform where small and medium enterprises (SMEs) can interact with an AI assistant to explore funding options, complete loan applications, and manage their financial profiles.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Features](#features)
3. [Technical Stack](#technical-stack)
4. [Project Structure](#project-structure)
5. [Authentication Flow](#authentication-flow)
6. [API Integration](#api-integration)
7. [Key Components](#key-components)
8. [Development Guide](#development-guide)

## Architecture Overview

The application follows a modern Next.js architecture with the following key characteristics:

- **Frontend**: Next.js 15 with App Router for server-side rendering and routing
- **Authentication**: Ory Hydra OAuth2 server for secure authentication
- **Real-time Communication**: WebSocket for chat functionality
- **API Communication**: RESTful APIs with automatic token refresh
- **State Management**: React Context API and custom hooks

## Features

### 1. AI-Powered Chat Assistant
- Real-time conversational interface
- AI assistant named "<PERSON><PERSON> <PERSON>" 
- Support for text messages and interactive action cards
- WebSocket-based communication for instant responses
- Context-aware responses for funding queries

### 2. Authentication System
- Email-based user identification
- OTP (One-Time Password) verification
- OAuth2 authorization code flow with Ory Hydra
- JWT token management (access, refresh, ID tokens)
- Automatic token refresh on expiration
- Secure cookie-based session management

### 3. Brand Profile Management
- **Members**: Add and manage team members associated with the brand
- **Bank Accounts**: Store and manage multiple bank account details
- **Documents**: Upload and organize business documents
- **Brand Information**: Maintain brand details and metadata

### 4. Dashboard
- Financial metrics overview
- Loan application status tracking
- Recent applications view
- Analytics and insights visualization

## Technical Stack

### Frontend
- **Framework**: Next.js 15.3.1
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **UI Components**: Custom components built on Radix UI primitives
- **Form Management**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Animations**: Framer Motion

### Authentication
- **Provider**: Ory Hydra OAuth2 server
- **Local Auth Service**: `http://127.0.0.1:3002/auth-svc/api/v1`
- **Token Storage**: Secure HTTP-only cookies

### API Integration
- **Backend API**: `http://localhost:5000/api`
- **WebSocket**: Real-time bidirectional communication
- **HTTP Client**: Axios with interceptors

## Project Structure

```
k2-customer-app/
├── src/
│   ├── app/                    # Next.js app router pages
│   │   ├── api/               # API routes
│   │   │   └── demands/       # Demands endpoint
│   │   ├── assistant/         # Chat interface page
│   │   ├── callback/          # OAuth callback handler
│   │   ├── dashboard/         # Business dashboard
│   │   ├── profile/           # Brand profile management
│   │   └── layout.tsx         # Root layout
│   │
│   ├── components/            # Reusable React components
│   │   ├── assistant/         # Chat-related components
│   │   │   ├── chat-interface.tsx
│   │   │   ├── chat-message.tsx
│   │   │   ├── action-card.tsx
│   │   │   └── auth-modal.tsx
│   │   ├── dashboard/         # Dashboard components
│   │   ├── layout/            # Layout components
│   │   │   ├── sidebar.tsx
│   │   │   ├── Header.tsx
│   │   │   └── Footer.tsx
│   │   ├── profile/           # Profile components
│   │   └── ui/                # Base UI components
│   │
│   ├── hooks/                 # Custom React hooks
│   │   ├── use-auth.tsx       # Authentication context
│   │   ├── use-websocket.tsx  # WebSocket management
│   │   ├── use-chat.ts        # Chat state management
│   │   └── use-uuid.ts        # Guest UUID generation
│   │
│   ├── lib/                   # Utility functions
│   │   ├── api-client.ts      # Axios instance
│   │   ├── auth.ts            # Auth utilities
│   │   └── utils.ts           # Helper functions
│   │
│   ├── services/              # API service layers
│   │   ├── api.ts             # General API calls
│   │   └── brand-service.ts   # Brand-related APIs
│   │
│   └── types/                 # TypeScript definitions
│       └── index.ts           # Type definitions
│
├── public/                    # Static assets
├── package.json              # Dependencies
├── next.config.ts            # Next.js configuration
├── tsconfig.json             # TypeScript configuration
└── tailwind.config.ts        # Tailwind CSS configuration
```

## Authentication Flow

### 1. User Identification
- User enters email address
- System checks if user exists or needs registration

### 2. OTP Generation
- OTP is sent to user's email
- User enters the received OTP

### 3. OAuth2 Flow
- Initiate OAuth2 authorization with Ory Hydra
- User consents to permissions
- Redirect to callback URL with authorization code

### 4. Token Exchange
- Exchange authorization code for tokens
- Receive access token, refresh token, and ID token
- Store tokens in secure HTTP-only cookies

### 5. Session Management
- Automatic token refresh on expiration
- Logout clears all authentication cookies
- Protected routes check authentication status

## API Integration

### API Client Configuration
```typescript
// Base configuration in lib/api-client.ts
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api',
  withCredentials: true,
});
```

### Automatic Token Refresh
The API client includes interceptors that:
1. Add authentication headers to all requests
2. Detect 401 responses indicating token expiration
3. Automatically refresh tokens using the refresh token
4. Retry the original request with new tokens

### WebSocket Integration
```typescript
// WebSocket connection in hooks/use-websocket.tsx
- Establishes persistent connection for real-time chat
- Handles reconnection on disconnection
- Routes messages between user and AI assistant
- Supports different message types (text, action cards)
```

## Key Components

### 1. Chat Interface (`chat-interface.tsx`)
- Main chat UI with message list and input
- Handles message sending and receiving
- Integrates with WebSocket for real-time updates
- Displays loading states and error handling

### 2. Action Cards (`action-card.tsx`)
- Dynamic UI cards for interactive workflows
- Support for different action types:
  - Authentication prompts
  - Form submissions
  - Information displays
- Contextual styling based on action type

### 3. Profile Form (`profile-form.tsx`)
- Comprehensive brand information form
- Integration with React Hook Form
- Zod validation for data integrity
- Auto-save functionality

### 4. Dashboard Metrics (`dashboard-metrics.tsx`)
- Key performance indicators display
- Real-time data updates
- Interactive charts and visualizations

## Development Guide

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager
- Local Ory Hydra instance (for authentication)
- Backend API server running

### Installation
```bash
# Clone the repository
git clone [repository-url]

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration
```

### Environment Variables
```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_WS_URL=ws://localhost:5000
NEXT_PUBLIC_ORY_URL=http://127.0.0.1:3002
```

### Running the Application
```bash
# Development mode
npm run dev

# Production build
npm run build
npm start

# Run tests
npm test

# Lint code
npm run lint
```

### Code Quality
- ESLint configuration for code linting
- Prettier for code formatting
- Husky pre-commit hooks
- Jest for unit testing
- React Testing Library for component testing

### Best Practices
1. **Component Structure**: Keep components small and focused
2. **Type Safety**: Use TypeScript interfaces for all data structures
3. **Error Handling**: Implement proper error boundaries and fallbacks
4. **Performance**: Use React.memo and useMemo where appropriate
5. **Security**: Never store sensitive data in local storage
6. **Accessibility**: Follow WCAG guidelines for all UI components

## Security Considerations

1. **Authentication**: All sensitive routes are protected
2. **Token Storage**: Uses HTTP-only cookies to prevent XSS attacks
3. **CORS**: Configured to accept requests only from allowed origins
4. **Input Validation**: All user inputs are validated on both client and server
5. **API Security**: All API calls include authentication headers
6. **Data Encryption**: Sensitive data is encrypted in transit (HTTPS)

## Deployment

The application is designed to be deployed on Vercel or similar platforms:

1. **Build**: Next.js optimizes the application during build
2. **Environment**: Set production environment variables
3. **Database**: Ensure backend services are accessible
4. **Monitoring**: Set up error tracking and performance monitoring
5. **Scaling**: Application supports horizontal scaling

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Ensure Ory Hydra is running
   - Check cookie settings in browser
   - Verify environment variables

2. **WebSocket Connection Failed**
   - Check if backend WebSocket server is running
   - Verify CORS settings
   - Check network firewall rules

3. **API Errors**
   - Ensure backend API is accessible
   - Check authentication tokens
   - Verify API endpoint URLs

### Debug Mode
Enable debug mode by setting:
```bash
DEBUG=true npm run dev
```

This provides additional logging for troubleshooting.

## Future Enhancements

1. **Multi-language Support**: Internationalization for global reach
2. **Advanced Analytics**: More detailed financial insights
3. **Mobile App**: Native mobile applications
4. **Offline Support**: Progressive Web App capabilities
5. **Advanced AI Features**: More sophisticated lending recommendations

## Contributing

Please follow these guidelines when contributing:
1. Create feature branches from `main`
2. Follow the existing code style
3. Write tests for new features
4. Update documentation as needed
5. Submit pull requests with clear descriptions

## License

This project is proprietary software owned by Klub. All rights reserved.