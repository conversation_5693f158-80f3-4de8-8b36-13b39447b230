# K2 Demand App

A Next.js application for conversational B2B SME lending.

## Features

- Modern React with Next.js 15
- TypeScript for type safety
- Tailwind CSS for styling
- API routes for backend functionality
- Component-based architecture
- Responsive design

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm or yarn

### Installation

1. Clone the repository:

```bash
git clone https://github.com/your-username/k2-demand-app.git
cd k2-demand-app
```

2. Install dependencies:

```bash
npm install
# or
yarn install
```

3. Create a `.env.local` file based on `.env.example`:

```bash
cp .env.example .env.local
```

Edit the `.env.local` file to match your local development environment.

4. Start the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Project Structure

```
k2-demand-app/
├── public/             # Static assets
├── src/
│   ├── app/            # App router pages and layouts
│   │   ├── api/        # API routes
│   │   └── ...         # Other routes
│   ├── components/     # React components
│   │   ├── layout/     # Layout components
│   │   └── ui/         # UI components
│   ├── hooks/          # Custom React hooks
│   ├── lib/            # Utility functions
│   ├── services/       # API services
│   └── types/          # TypeScript types
├── .env.example        # Environment variables template
├── .env.local          # Local environment variables (not in git)
├── .env.production.example # Production environment template
├── next.config.mjs     # Next.js configuration
└── ...                 # Other configuration files
```

## Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the application for production
- `npm run start` - Start the production server
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run format` - Format code with Prettier

## Environment Configuration

The application uses environment variables for configuration. Different environments are supported:

### Development

- Copy `.env.example` to `.env.local`
- Modify values in `.env.local` for your local setup
- The app will automatically use these values when `NODE_ENV` is not `production`

### Production

- Set environment variables directly in your deployment environment
- Or copy `.env.production.example` to `.env.production` for production-specific values
- Docker Compose will use production defaults if no environment variables are set

### Key Environment Variables

| Variable                         | Description                | Development Default                          | Production Default                           |
| -------------------------------- | -------------------------- | -------------------------------------------- | -------------------------------------------- |
| `NEXT_PUBLIC_WEBSOCKET_URL`      | WebSocket server URL       | `ws://localhost:3001/ws/`                    | `wss://k2-api.klubworks.com/ws/`             |
| `NEXT_PUBLIC_AUTH_SERVICE_URL`   | Authentication service URL | `http://localhost:3002/auth-svc/api/v1`      | `http://127.0.0.1:3002/auth-svc/api/v1`      |
| `NEXT_PUBLIC_SERVICE_BASE_URL`   | Main backend API URL       | `http://localhost:8080/api/conversation-svc` | `http://127.0.0.1:8080/api/conversation-svc` |
| `NEXT_PUBLIC_OAUTH_CLIENT_ID`    | OAuth client identifier    | `demand-app-client-dev`                      | `demand-app-client`                          |
| `NEXT_PUBLIC_OAUTH_REDIRECT_URL` | OAuth redirect URL         | `http://localhost:3000/assistant`            | `http://127.0.0.1:3000/assistant`            |

## Best Practices Implemented

- **TypeScript** for type safety
- **ESLint** and **Prettier** for code quality
- **Jest** and **React Testing Library** for testing
- **Husky** and **lint-staged** for pre-commit hooks
- **Component-based architecture** for reusability
- **API services** for data fetching
- **Custom hooks** for shared logic
- **Error boundaries** for graceful error handling
- **Loading states** for better UX
- **Responsive design** with Tailwind CSS
- **Accessibility** considerations
- **Security headers** with Next.js config

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

## License

This project is licensed under the MIT License.
