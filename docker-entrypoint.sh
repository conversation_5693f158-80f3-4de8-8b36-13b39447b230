#!/bin/sh
set -e

# Function to replace placeholders in JavaScript files
replace_env_vars() {
    # Find all JS files in .next/static
    find .next/static -type f -name "*.js" | while read -r file; do
        # Replace runtime placeholders with actual environment variables
        if [ -n "$NEXT_PUBLIC_WEBSOCKET_URL" ]; then
            sed -i "s|RUNTIME_NEXT_PUBLIC_WEBSOCKET_URL|${NEXT_PUBLIC_WEBSOCKET_URL}|g" "$file"
        fi

        if [ -n "$NEXT_PUBLIC_AUTH_SERVICE_URL" ]; then
            sed -i "s|RUNTIME_NEXT_PUBLIC_AUTH_SERVICE_URL|${NEXT_PUBLIC_AUTH_SERVICE_URL}|g" "$file"
        fi

        if [ -n "$NEXT_PUBLIC_SERVICE_BASE_URL" ]; then
            sed -i "s|RUNTIME_NEXT_PUBLIC_SERVICE_BASE_URL|${NEXT_PUBLIC_SERVICE_BASE_URL}|g" "$file"
        fi

        if [ -n "$NEXT_PUBLIC_OAUTH_CLIENT_ID" ]; then
            sed -i "s|RUNTIME_NEXT_PUBLIC_OAUTH_CLIENT_ID|${NEXT_PUBLIC_OAUTH_CLIENT_ID}|g" "$file"
        fi

        if [ -n "$NEXT_PUBLIC_OAUTH_REDIRECT_URL" ]; then
            sed -i "s|RUNTIME_NEXT_PUBLIC_OAUTH_REDIRECT_URL|${NEXT_PUBLIC_OAUTH_REDIRECT_URL}|g" "$file"
        fi
    done

    # Also replace in server.js for server-side rendering
    if [ -f "server.js" ]; then
        if [ -n "$NEXT_PUBLIC_WEBSOCKET_URL" ]; then
            sed -i "s|RUNTIME_NEXT_PUBLIC_WEBSOCKET_URL|${NEXT_PUBLIC_WEBSOCKET_URL}|g" "server.js"
        fi

        if [ -n "$NEXT_PUBLIC_AUTH_SERVICE_URL" ]; then
            sed -i "s|RUNTIME_NEXT_PUBLIC_AUTH_SERVICE_URL|${NEXT_PUBLIC_AUTH_SERVICE_URL}|g" "server.js"
        fi

        if [ -n "$NEXT_PUBLIC_SERVICE_BASE_URL" ]; then
            sed -i "s|RUNTIME_NEXT_PUBLIC_SERVICE_BASE_URL|${NEXT_PUBLIC_SERVICE_BASE_URL}|g" "server.js"
        fi

        if [ -n "$NEXT_PUBLIC_OAUTH_CLIENT_ID" ]; then
            sed -i "s|RUNTIME_NEXT_PUBLIC_OAUTH_CLIENT_ID|${NEXT_PUBLIC_OAUTH_CLIENT_ID}|g" "server.js"
        fi

        if [ -n "$NEXT_PUBLIC_OAUTH_REDIRECT_URL" ]; then
            sed -i "s|RUNTIME_NEXT_PUBLIC_OAUTH_REDIRECT_URL|${NEXT_PUBLIC_OAUTH_REDIRECT_URL}|g" "server.js"
        fi
    fi
}

# Replace environment variables
echo "Injecting runtime environment variables..."
replace_env_vars

# Execute the main command
exec "$@"