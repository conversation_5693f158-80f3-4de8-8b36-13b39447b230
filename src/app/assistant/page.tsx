'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { ChatInterface } from '@/components/assistant/chat-interface';
import { LoginModal } from '@/components/assistant/login-modal';
import { SignupModal } from '@/components/assistant/signup-modal';
import {
  setCookie,
  EMAIL_COOKIE,
  FLOW_ID_COOKIE,
  FLOW_TYPE_COOKIE,
  getCookie,
  ACCESS_TOKEN_COOKIE,
} from '@/lib/auth';
import { useAuth } from '@/hooks/use-auth';

export default function AssistantPage() {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isSignupModalOpen, setIsSignupModalOpen] = useState(false);
  const [initialFlowId, setInitialFlowId] = useState<string | undefined>();
  const [initialFlowType, setInitialFlowType] = useState<'login' | 'registration'>('login');
  const [initialEmail, setInitialEmail] = useState<string | undefined>();

  const { user } = useAuth();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (user) {
      return;
    }

    const flow = searchParams.get('flow') || searchParams.get('flowId');
    const code = searchParams.get('code');
    const flowType =
      (searchParams.get('flowType') as 'login' | 'registration') ||
      (getCookie(FLOW_TYPE_COOKIE) as 'login' | 'registration') ||
      'login';
    const email = searchParams.get('email') || getCookie(EMAIL_COOKIE) || undefined;

    if (flow || code) {
      // If we have a flow ID or code, prepare to open the appropriate modal
      setInitialFlowId(flow || undefined);
      setInitialFlowType(flowType);
      setInitialEmail(email);

      // Store values in cookies for later use
      if (flow) setCookie(FLOW_ID_COOKIE, flow);
      if (flowType) setCookie(FLOW_TYPE_COOKIE, flowType);
      if (email) setCookie(EMAIL_COOKIE, email);

      // Clean up URL parameters if we have a code
      // This prevents the code from being reused on page refresh
      if (
        code &&
        typeof window !== 'undefined' &&
        getCookie(ACCESS_TOKEN_COOKIE) &&
        getCookie(ACCESS_TOKEN_COOKIE) !== ''
      ) {
        const url = new URL(window.location.href);
        url.searchParams.delete('code');
        window.history.replaceState({}, '', url.toString());
      }

      console.log('Flow type from URL or cookie:', flowType);

      // Open the appropriate modal based on the flow type
      if (flowType === 'registration') {
        setIsSignupModalOpen(true);
        setIsLoginModalOpen(false);
      } else {
        setIsLoginModalOpen(true);
        setIsSignupModalOpen(false);
      }
    }
  }, [searchParams, user]);

  return (
    <div className="flex flex-col h-full bg-white">
      <div
        className="flex-shrink-0 p-4 flex justify-between items-center"
        style={{
          background: 'linear-gradient(180deg, #FFFFFF 77.19%, rgba(255, 255, 255, 0.4) 136.87%)',
          height: '80px',
        }}
      >
        <div className="flex items-center mx-auto" style={{ width: '870px' }}>
          <h1 className="text-2xl font-bold">Klub AI</h1>
          <span className="ml-2 px-1 py-0.5 text-xs bg-[#F4F8AC] rounded text-black">New</span>
        </div>
      </div>
      <div className="flex-grow overflow-hidden">
        <ChatInterface />
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        initialFlowId={initialFlowId}
        initialFlowType={initialFlowType}
        initialEmail={initialEmail}
        onSwitchToSignup={(email) => {
          setIsLoginModalOpen(false);
          setIsSignupModalOpen(true);
          setInitialEmail(email);
          setInitialFlowType('registration');
        }}
      />

      {/* Signup Modal */}
      <SignupModal
        isOpen={isSignupModalOpen}
        onClose={() => setIsSignupModalOpen(false)}
        initialFlowId={initialFlowId}
        initialFlowType="registration"
        initialEmail={initialEmail}
        onSwitchToLogin={(email) => {
          setIsSignupModalOpen(false);
          setIsLoginModalOpen(true);
          setInitialEmail(email);
          setInitialFlowType('login');
        }}
      />
    </div>
  );
}
