import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { DashboardMetrics } from '@/components/dashboard/dashboard-metrics';
import { RecentApplications } from '@/components/dashboard/recent-applications';
import { LoanOverview } from '@/components/dashboard/loan-overview';

export default function DashboardPage() {
  return (
    <div className="flex flex-col h-full">
      <div className="flex-none">
        <h1 className="text-2xl font-bold mb-4">Dashboard</h1>
        <p className="text-gray-500 mb-6">
          Overview of your lending applications and financial metrics.
        </p>
      </div>

      <div className="flex-grow">
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="applications">Applications</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <DashboardMetrics />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <RecentApplications />
              <LoanOverview />
            </div>
          </TabsContent>

          <TabsContent value="applications">
            <Card>
              <CardHeader>
                <CardTitle>Loan Applications</CardTitle>
                <CardDescription>View and manage all your loan applications.</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Applications content will be displayed here.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>Analytics</CardTitle>
                <CardDescription>
                  Detailed analytics and reports for your lending activities.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>Analytics content will be displayed here.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
