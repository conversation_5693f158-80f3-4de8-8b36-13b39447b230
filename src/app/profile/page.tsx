import { MembersList } from '@/components/profile/members-list';
import { BankAccounts } from '@/components/profile/bank-accounts';
import { Documents } from '@/components/profile/documents';
import { BrandHeader } from '@/components/profile/brand-header';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

export default function ProfilePage() {
  return (
    <div className="flex flex-col h-full overflow-hidden">
      <div className="flex-none px-6 py-4 border-b border-gray-200">
        <div className="flex items-center gap-2 mb-1">
          <BrandHeader />
        </div>
      </div>

      <div className="flex-grow overflow-hidden">
        <Tabs defaultValue="members" className="h-full flex flex-col">
          <div className="flex flex-col px-[38px] py-0 gap-[10px] bg-[#F3F4F6] border-b border-[#D0D3DD]">
            <TabsList className="bg-transparent p-0 h-[41px] w-auto space-x-6">
              <TabsTrigger
                value="members"
                className="w-[107px] h-[41px] flex flex-col items-center px-0 py-3 rounded-none border-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-600"
              >
                Members
              </TabsTrigger>
              <TabsTrigger
                value="bank-accounts"
                className="w-[107px] h-[41px] flex flex-col items-center px-0 py-3 rounded-none border-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-600"
              >
                Bank Details
              </TabsTrigger>
              <TabsTrigger
                value="documents"
                className="w-[107px] h-[41px] flex flex-col items-center px-0 py-3 rounded-none border-0 data-[state=active]:shadow-none data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-600"
              >
                Documents
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="members" className="pt-6 flex-1 overflow-auto">
            <div className="flex flex-col items-start px-[38px] gap-[20px] w-full max-w-[1092px] mx-auto pb-20">
              <div className="flex justify-between items-center w-full mb-4">
                {/* <h2 className="text-xl font-semibold dm-sans">Team Members</h2> */}
                <div className="flex items-center"></div>
              </div>
              <div className="w-full">
                <MembersList />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="bank-accounts" className="pt-6 flex-1 overflow-auto">
            <div className="flex flex-col items-start px-[38px] gap-[20px] w-full max-w-[1092px] mx-auto pb-20">
              <div className="flex justify-between items-center w-full mb-4">
                {/* <BrandTitle /> */}
                <div className="flex items-center"></div>
              </div>
              <div className="w-full">
                <BankAccounts />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="documents" className="pt-6 flex-1 overflow-auto">
            <div className="flex flex-col items-start px-[38px] gap-[20px] w-full max-w-[1092px] mx-auto pb-20">
              <div className="flex justify-between items-center w-full mb-4">
                {/* <BrandTitle /> */}
                <div className="flex items-center"></div>
              </div>
              <div className="w-full">
                <Documents />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
