'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { setCookie, FLOW_ID_COOKIE, FLOW_TYPE_COOKIE, EMAIL_COOKIE } from '@/lib/auth';
import { LoginModal } from '@/components/assistant/login-modal';

export default function SessionPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const router = useRouter();
  
  useEffect(() => {
    const initializeSession = async () => {
      try {
        const flowId = searchParams.get('flowId');
        const flowType = searchParams.get('flowType') || 'login';
        const email = searchParams.get('email');
        
        if (!flowId) {
          throw new Error('No flow ID found in URL');
        }
        
        // Store flow information in cookies
        setCookie(FLOW_ID_COOKIE, flowId);
        setCookie(FLOW_TYPE_COOKIE, flowType);
        if (email) setCookie(EMAIL_COOKIE, email);
        
        // Open the login modal
        setIsModalOpen(true);
        
        // We'll pass the flowId to the modal via URL parameters
        // The modal will handle generating the OTP
      } catch (error) {
        console.error('Session initialization error:', error);
        setError(error instanceof Error ? error.message : 'Failed to initialize session');
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeSession();
  }, [searchParams, router]);
  
  const handleCloseModal = () => {
    setIsModalOpen(false);
    router.push('/');
  };
  
  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md text-center">
          <h1 className="text-2xl font-bold">Initializing Session</h1>
          <div className="mt-4">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Please wait while we set up your session...</p>
          </div>
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md text-center">
          <h1 className="text-2xl font-bold text-red-600">Session Error</h1>
          <p className="mt-2 text-gray-600">{error}</p>
          <button
            onClick={() => router.push('/')}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Home
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <LoginModal 
        isOpen={isModalOpen} 
        onClose={handleCloseModal}
        initialFlowId={searchParams.get('flowId') || undefined}
        initialFlowType={(searchParams.get('flowType') as 'login' | 'registration') || 'login'}
        initialEmail={searchParams.get('email') || undefined}
      />
      
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md text-center">
        <h1 className="text-2xl font-bold">Session Initialized</h1>
        <p className="mt-2 text-gray-600">
          Please complete the authentication process in the dialog.
        </p>
      </div>
    </div>
  );
}
