'use client';

import { useState } from 'react';
import { ActionRequestMessage } from '@/hooks/use-websocket';
import { SummaryModal } from './summary-modal';
import { FormModal } from './form-modal';

interface ActionCardProps {
  message: ActionRequestMessage;
  onOpenAuthModal?: () => void;
  onSendEscalationRequest?: () => void;
}

export function ActionCard({ message, onOpenAuthModal, onSendEscalationRequest }: ActionCardProps) {
  const [isSummaryModalOpen, setIsSummaryModalOpen] = useState(false);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isEscalationSent, setIsEscalationSent] = useState(false);
  const { category, data } = message.payload;
  const { heading, description, apiEndpoint, url, formFields, buttonText } = data;

  const getCategoryColors = (cat: string) => {
    switch (cat) {
      case 'login':
        return {
          background: 'rgba(155, 129, 245, 0.1)',
          buttonColor: '#9B81F5',
          buttonHover: '#8A70E4',
        };
      case 'escalation':
        return {
          background: 'rgba(239, 68, 68, 0.1)',
          buttonColor: '#EF4444',
          buttonHover: '#DC2626',
        };
      case 'summary':
        return {
          background: 'rgba(59, 130, 246, 0.1)',
          buttonColor: '#3B82F6',
          buttonHover: '#2563EB',
        };
      case 'form':
        return {
          background: 'rgba(34, 197, 94, 0.1)',
          buttonColor: '#22C55E',
          buttonHover: '#16A34A',
        };
      case 'content':
        return {
          background: 'rgba(168, 85, 247, 0.1)',
          buttonColor: '#A855F7',
          buttonHover: '#9333EA',
        };
      case 'offer':
        return {
          background: 'rgba(245, 158, 11, 0.1)',
          buttonColor: '#F59E0B',
          buttonHover: '#D97706',
        };
      default:
        return {
          background: 'rgba(224, 249, 241, 0.8)',
          buttonColor: '#01744F',
          buttonHover: '#065F46',
        };
    }
  };

  const colors = getCategoryColors(category);

  const handleActionClick = () => {
    switch (category) {
      case 'summary':
        setIsSummaryModalOpen(true);
        break;
      case 'form':
        if (formFields && formFields.length > 0) {
          setIsFormModalOpen(true);
        } else {
          console.log('No form fields provided');
        }
        break;
      case 'content':
        if (url) {
          // Open URL in new tab
          window.open(url, '_blank', 'noopener,noreferrer');
        } else {
          console.log('No URL provided for content');
        }
        break;
      case 'login':
        if (onOpenAuthModal) {
          onOpenAuthModal();
        } else {
          console.log('Login action clicked but no auth modal handler provided');
        }
        break;
      case 'escalation':
        if (onSendEscalationRequest && !isEscalationSent) {
          onSendEscalationRequest();
          setIsEscalationSent(true);
        } else if (isEscalationSent) {
          console.log('Escalation request already sent');
        } else {
          console.log('Escalation action clicked but no handler provided');
        }
        break;
      case 'offer':
        // TODO: Handle offer action
        console.log('Offer action clicked');
        break;
      default:
        console.log('Unknown action category:', category);
    }
  };

  const getButtonText = (): string => {
    if (category === 'escalation' && isEscalationSent) {
      return 'Request Successful';
    }

    if (buttonText && typeof buttonText === 'string') {
      return buttonText;
    }

    switch (category) {
      case 'summary':
        return 'View More';
      case 'form':
        return 'Fill Form';
      case 'content':
        return 'View Content';
      case 'login':
        return 'Signup/Login';
      case 'escalation':
        return 'Talk to Human Operator';
      case 'offer':
        return 'View Offer';
      default:
        return 'View';
    }
  };

  return (
    <>
      {/* Action Card with Figma CSS */}
      <div
        className="flex flex-col items-start p-6 sm:p-8 gap-2 sm:gap-3 w-full max-w-[420px] min-h-[221px] rounded-md"
        style={{
          background: colors.background,
        }}
      >
        {/* Heading and Description Container */}
        <div className="flex flex-col items-start w-full gap-3 sm:gap-4 flex-grow">
          {/* Heading */}
          <h3 className="text-base sm:text-lg font-semibold text-gray-900 opacity-80 w-full">
            {heading}
          </h3>

          {/* Description */}
          <p className="text-sm sm:text-base text-gray-700 opacity-80 w-full flex-grow">
            {description}
          </p>

          {/* Button */}
          <button
            onClick={handleActionClick}
            disabled={category === 'escalation' && isEscalationSent}
            className="flex justify-center items-center px-3 sm:px-4 py-3 rounded-sm text-white text-sm sm:text-base font-normal min-h-[45px] w-full max-w-[162px] transition-colors"
            style={{
              background:
                category === 'escalation' && isEscalationSent ? '#9CA3AF' : colors.buttonColor,
              cursor: category === 'escalation' && isEscalationSent ? 'not-allowed' : 'pointer',
            }}
          >
            <span className="text-center break-words">{getButtonText()}</span>
          </button>
        </div>
      </div>

      {/* Summary Modal */}
      {category === 'summary' && (
        <SummaryModal
          isOpen={isSummaryModalOpen}
          onClose={() => setIsSummaryModalOpen(false)}
          apiEndpoint={apiEndpoint}
          heading={heading}
        />
      )}

      {/* Form Modal */}
      {category === 'form' && formFields && (
        <FormModal
          isOpen={isFormModalOpen}
          onClose={() => setIsFormModalOpen(false)}
          formFields={formFields}
          heading={heading}
          description={description}
        />
      )}
    </>
  );
}
