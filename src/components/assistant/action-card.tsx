'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Eye, FileText, LogIn, DollarSign, BookOpen } from 'lucide-react';
import { ActionRequestMessage } from '@/hooks/use-websocket';
import { SummaryModal } from './summary-modal';
import { FormModal } from './form-modal';

interface ActionCardProps {
  message: ActionRequestMessage;
}

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'summary':
      return <Eye className="w-5 h-5" />;
    case 'form':
      return <FileText className="w-5 h-5" />;
    case 'login':
      return <LogIn className="w-5 h-5" />;
    case 'offer':
      return <DollarSign className="w-5 h-5" />;
    case 'content':
      return <BookOpen className="w-5 h-5" />;
    default:
      return <FileText className="w-5 h-5" />;
  }
};

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'summary':
      return 'bg-blue-100 text-blue-600';
    case 'form':
      return 'bg-green-100 text-green-600';
    case 'login':
      return 'bg-purple-100 text-purple-600';
    case 'offer':
      return 'bg-yellow-100 text-yellow-600';
    case 'content':
      return 'bg-gray-100 text-gray-600';
    default:
      return 'bg-gray-100 text-gray-600';
  }
};

export function ActionCard({ message }: ActionCardProps) {
  const [isSummaryModalOpen, setIsSummaryModalOpen] = useState(false);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const { category, data } = message.payload;
  const { heading, description, apiEndpoint, url, formFields } = data;

  const handleActionClick = () => {
    switch (category) {
      case 'summary':
        setIsSummaryModalOpen(true);
        break;
      case 'form':
        if (formFields && formFields.length > 0) {
          setIsFormModalOpen(true);
        } else {
          console.log('No form fields provided');
        }
        break;
      case 'content':
        if (url) {
          // Open URL in new tab
          window.open(url, '_blank', 'noopener,noreferrer');
        } else {
          console.log('No URL provided for content');
        }
        break;
      case 'login':
        // TODO: Handle login action
        console.log('Login action clicked');
        break;
      case 'offer':
        // TODO: Handle offer action
        console.log('Offer action clicked');
        break;
      default:
        console.log('Unknown action category:', category);
    }
  };

  const getButtonText = () => {
    switch (category) {
      case 'summary':
        return 'View More';
      case 'form':
        return 'Fill Form';
      case 'content':
        return 'View Content';
      case 'login':
        return 'Login';
      case 'offer':
        return 'View Offer';
      default:
        return 'View';
    }
  };

  return (
    <>
      <Card className="w-full max-w-md border border-gray-200 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center ${getCategoryColor(category)}`}
              >
                {getCategoryIcon(category)}
              </div>
            </div>
            <div className="flex-grow">
              <h3 className="text-sm font-medium text-gray-900 mb-1">{heading}</h3>
              <p className="text-sm text-gray-600 mb-3">{description}</p>
              <Button
                onClick={handleActionClick}
                size="sm"
                className="bg-[#9B81F5] hover:bg-[#8A70E4] text-white"
              >
                {getButtonText()}
              </Button>
            </div>
          </div>
          <div className="mt-3 pt-3 border-t border-gray-100">
            <p className="text-xs text-gray-500">
              <strong>Category:</strong> {category} • <strong>From:</strong> {message.sender}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Summary Modal */}
      {category === 'summary' && (
        <SummaryModal
          isOpen={isSummaryModalOpen}
          onClose={() => setIsSummaryModalOpen(false)}
          apiEndpoint={apiEndpoint}
          heading={heading}
        />
      )}

      {/* Form Modal */}
      {category === 'form' && formFields && (
        <FormModal
          isOpen={isFormModalOpen}
          onClose={() => setIsFormModalOpen(false)}
          formFields={formFields}
          heading={heading}
          description={description}
        />
      )}
    </>
  );
}
