'use client';

import { useState } from 'react';
import { ActionRequestMessage } from '@/hooks/use-websocket';
import { SummaryModal } from './summary-modal';
import { FormModal } from './form-modal';

interface ActionCardProps {
  message: ActionRequestMessage;
  onOpenAuthModal?: () => void;
  onSendEscalationRequest?: () => void;
}

export function ActionCard({ message, onOpenAuthModal, onSendEscalationRequest }: ActionCardProps) {
  const [isSummaryModalOpen, setIsSummaryModalOpen] = useState(false);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isEscalationSent, setIsEscalationSent] = useState(false);
  const { category, data } = message.payload;
  const { heading, description, apiEndpoint, url, formFields, buttonText } = data;

  const handleActionClick = () => {
    switch (category) {
      case 'summary':
        setIsSummaryModalOpen(true);
        break;
      case 'form':
        if (formFields && formFields.length > 0) {
          setIsFormModalOpen(true);
        } else {
          console.log('No form fields provided');
        }
        break;
      case 'content':
        if (url) {
          // Open URL in new tab
          window.open(url, '_blank', 'noopener,noreferrer');
        } else {
          console.log('No URL provided for content');
        }
        break;
      case 'login':
        if (onOpenAuthModal) {
          onOpenAuthModal();
        } else {
          console.log('Login action clicked but no auth modal handler provided');
        }
        break;
      case 'escalation':
        if (onSendEscalationRequest && !isEscalationSent) {
          onSendEscalationRequest();
          setIsEscalationSent(true);
        } else if (isEscalationSent) {
          console.log('Escalation request already sent');
        } else {
          console.log('Escalation action clicked but no handler provided');
        }
        break;
      case 'offer':
        // TODO: Handle offer action
        console.log('Offer action clicked');
        break;
      default:
        console.log('Unknown action category:', category);
    }
  };

  const getButtonText = (): string => {
    // Special case for escalation when request is sent
    if (category === 'escalation' && isEscalationSent) {
      return 'Request Successful';
    }

    // Use buttonText from data if available, otherwise use default based on category
    if (buttonText && typeof buttonText === 'string') {
      return buttonText;
    }

    switch (category) {
      case 'summary':
        return 'View More';
      case 'form':
        return 'Fill Form';
      case 'content':
        return 'View Content';
      case 'login':
        return 'Signup/Login';
      case 'escalation':
        return 'Talk to Human Operator';
      case 'offer':
        return 'View Offer';
      default:
        return 'View';
    }
  };

  return (
    <>
      {/* Action Card with Figma CSS */}
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          padding: '30px 0px 30px 30px',
          gap: '10px',
          width: '420px',
          height: '221px',
          background: 'rgba(224, 249, 241, 0.8)',
          borderRadius: '4px',
          flex: 'none',
          order: 0,
          flexGrow: 0,
        }}
      >
        {/* Heading and Description Container */}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            padding: '0px',
            gap: '15px', // Reduced from 77px to fit better
            width: '364px',
            height: '161px',
            flex: 'none',
            order: 0,
            flexGrow: 0,
          }}
        >
          {/* Heading */}
          <h3
            style={{
              width: '291px',
              height: '23px',
              fontFamily: 'DM Sans',
              fontStyle: 'normal',
              fontWeight: 600,
              fontSize: '18px',
              lineHeight: '23px',
              display: 'flex',
              alignItems: 'center',
              color: '#1D1D1B',
              opacity: 0.8,
              flex: 'none',
              order: 0,
              flexGrow: 0,
            }}
          >
            {heading}
          </h3>

          {/* Description */}
          <p
            style={{
              width: '364px',
              height: '63px',
              fontFamily: 'DM Sans',
              fontStyle: 'normal',
              fontWeight: 400,
              fontSize: '16px',
              lineHeight: '21px',
              display: 'flex',
              alignItems: 'center',
              color: 'rgba(29, 29, 27, 0.8)',
              opacity: 0.8,
              flex: 'none',
              order: 1,
              flexGrow: 0,
            }}
          >
            {description}
          </p>

          {/* Button */}
          <button
            onClick={handleActionClick}
            disabled={category === 'escalation' && isEscalationSent}
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '0px 14px',
              gap: '6px',
              width: '162px',
              height: '45px',
              background: category === 'escalation' && isEscalationSent ? '#9CA3AF' : '#01744F',
              borderRadius: '3px',
              border: 'none',
              cursor: category === 'escalation' && isEscalationSent ? 'not-allowed' : 'pointer',
              flex: 'none',
              order: 2,
              flexGrow: 0,
            }}
          >
            <span
              style={{
                width: '134px',
                height: '21px',
                fontFamily: 'DM Sans',
                fontStyle: 'normal',
                fontWeight: 400,
                fontSize: '16px',
                lineHeight: '21px',
                color: '#FFFFFF',
                flex: 'none',
                order: 1,
                flexGrow: 0,
              }}
            >
              {getButtonText()}
            </span>
          </button>
        </div>
      </div>

      {/* Summary Modal */}
      {category === 'summary' && (
        <SummaryModal
          isOpen={isSummaryModalOpen}
          onClose={() => setIsSummaryModalOpen(false)}
          apiEndpoint={apiEndpoint}
          heading={heading}
        />
      )}

      {/* Form Modal */}
      {category === 'form' && formFields && (
        <FormModal
          isOpen={isFormModalOpen}
          onClose={() => setIsFormModalOpen(false)}
          formFields={formFields}
          heading={heading}
          description={description}
        />
      )}
    </>
  );
}
