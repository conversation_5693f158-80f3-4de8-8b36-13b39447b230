'use client';

import { ActionCard } from './action-card';
import { ActionRequestMessage } from '@/hooks/use-websocket';

interface ActionCardsContainerProps {
  actionMessages: ActionRequestMessage[];
  onOpenAuthModal?: () => void;
  onSendEscalationRequest?: () => void;
}

export function ActionCardsContainer({
  actionMessages,
  onOpenAuthModal,
  onSendEscalationRequest,
}: ActionCardsContainerProps) {
  if (actionMessages.length === 0) {
    return null;
  }

  if (actionMessages.length === 1) {
    return (
      <div style={{ paddingLeft: '14px' }}>
        <ActionCard
          message={actionMessages[0]}
          onOpenAuthModal={onOpenAuthModal}
          onSendEscalationRequest={onSendEscalationRequest}
        />
      </div>
    );
  }

  // Multiple cards - side by side on desktop, stacked on mobile
  return (
    <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 w-full max-w-[870px] px-4 sm:px-0">
      {actionMessages.slice(0, 2).map((actionMessage, index) => (
        <ActionCard
          key={index}
          message={actionMessage}
          onOpenAuthModal={onOpenAuthModal}
          onSendEscalationRequest={onSendEscalationRequest}
        />
      ))}
    </div>
  );
}
