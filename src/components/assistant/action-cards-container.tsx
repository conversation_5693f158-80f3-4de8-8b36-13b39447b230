'use client';

import { ActionCard } from './action-card';
import { ActionRequestMessage } from '@/hooks/use-websocket';

interface ActionCardsContainerProps {
  actionMessages: ActionRequestMessage[];
  onOpenAuthModal?: () => void;
  onSendEscalationRequest?: () => void;
}

export function ActionCardsContainer({
  actionMessages,
  onOpenAuthModal,
  onSendEscalationRequest,
}: ActionCardsContainerProps) {
  if (actionMessages.length === 0) {
    return null;
  }

  if (actionMessages.length === 1) {
    return (
      <div style={{ paddingLeft: '14px' }}>
        <ActionCard
          message={actionMessages[0]}
          onOpenAuthModal={onOpenAuthModal}
          onSendEscalationRequest={onSendEscalationRequest}
        />
      </div>
    );
  }

  return (
    <div
      style={{
        paddingLeft: '14px',
        paddingRight: '14px',
        display: 'flex',
        justifyContent: 'space-between',
        width: '870px',
        gap: '30px',
      }}
    >
      {actionMessages.slice(0, 2).map((actionMessage, index) => (
        <ActionCard
          key={index}
          message={actionMessage}
          onOpenAuthModal={onOpenAuthModal}
          onSendEscalationRequest={onSendEscalationRequest}
        />
      ))}
    </div>
  );
}
