'use client';

import React, { useState, useEffect } from 'react';
import { LoginModal } from './login-modal';
import { SignupModal } from './signup-modal';
import { checkUserExists } from '@/lib/auth';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialFlowId?: string;
  initialFlowType?: 'login' | 'registration';
  initialEmail?: string;
  onAuthSuccess?: () => void;
}

export function AuthModal({
  isOpen,
  onClose,
  initialFlowId,
  initialFlowType = 'login',
  initialEmail,
}: AuthModalProps) {
  const [showLogin, setShowLogin] = useState(initialFlowType === 'login');
  const [showSignup, setShowSignup] = useState(initialFlowType === 'registration');
  const [email, setEmail] = useState(initialEmail || '');
  const [isCheckingUser, setIsCheckingUser] = useState(false);
  const emailRef = React.useRef(email);
  const checkInProgressRef = React.useRef(false);

  useEffect(() => {
    const checkUser = async () => {
      if (!isOpen || !email || isCheckingUser || checkInProgressRef.current) return;

      try {
        checkInProgressRef.current = true;
        setIsCheckingUser(true);
        const flowType = await checkUserExists(email);

        setShowLogin(flowType === 'login');
        setShowSignup(flowType === 'registration');
      } catch (error) {
        console.error('Error checking user existence:', error);
        setShowLogin(true);
        setShowSignup(false);
      } finally {
        setIsCheckingUser(false);
        checkInProgressRef.current = false;
      }
    };

    if (isOpen && email) {
      const lastCheckedEmail = emailRef.current;
      if (email !== lastCheckedEmail || (!showLogin && !showSignup)) {
        checkUser();
      }
    } else if (isOpen) {
      setShowLogin(initialFlowType === 'login');
      setShowSignup(initialFlowType === 'registration');
    }

    emailRef.current = email;
  }, [isOpen, email, initialFlowType]);

  const handleSwitchToSignup = (email: string) => {
    setEmail(email);
    setShowLogin(false);
    setShowSignup(true);
  };

  const handleSwitchToLogin = (email: string) => {
    setEmail(email);
    setShowLogin(true);
    setShowSignup(false);
  };

  const handleClose = () => {
    setShowLogin(false);
    setShowSignup(false);
    setEmail('');
    onClose();
  };

  return (
    <>
      {showLogin && (
        <LoginModal
          isOpen={isOpen && showLogin}
          onClose={handleClose}
          initialFlowId={initialFlowId}
          initialFlowType="login"
          initialEmail={email}
          onSwitchToSignup={handleSwitchToSignup}
        />
      )}

      {showSignup && (
        <SignupModal
          isOpen={isOpen && showSignup}
          onClose={handleClose}
          initialFlowId={initialFlowId}
          initialFlowType="registration"
          initialEmail={email}
          onSwitchToLogin={handleSwitchToLogin}
        />
      )}
    </>
  );
}
