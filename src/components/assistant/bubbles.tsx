'use client';

interface BubblesProps {
  suggestions: string[];
  onBubbleClick: (suggestion: string) => void;
}

export function Bubbles({ suggestions, onBubbleClick }: BubblesProps) {
  if (!suggestions || suggestions.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-row flex-wrap justify-center items-center align-content-center gap-[10px] w-[870px] mx-auto">
      {suggestions.map((suggestion, index) => (
        <button
          key={index}
          onClick={() => onBubbleClick(suggestion)}
          className="
            flex flex-row justify-center items-center
            px-[14px] py-0 gap-[6px]
            h-[42px] min-w-fit
            bg-white/80
            border-[1.6px] border-[#9D9D9D]
            rounded-[50px]
            hover:bg-white/90 hover:border-[#8A70E4]
            active:bg-white/95 active:border-[#7A60D4]
            transition-all duration-200
            text-sm font-normal text-gray-700
            whitespace-nowrap
            cursor-pointer
            focus:outline-none focus:ring-2 focus:ring-[#9B81F5] focus:ring-opacity-50
          "
        >
          {suggestion}
        </button>
      ))}
    </div>
  );
}
