'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ChatMessage } from './chat-message';

import { Bubbles } from './bubbles';
import { useWebSocket } from '@/hooks/use-websocket';
import { AuthModal } from './auth-modal';
import Image from 'next/image';

export function ChatInterface() {
  const [input, setInput] = useState('');
  const [currentBubbles, setCurrentBubbles] = useState<string[]>([]);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const {
    messages,
    sendMessage,
    sendEscalationRequest,
    isLoading,
    isConnected,
    isReady,
    loadMoreHistory,
    isLoadingHistory,
    hasMoreHistory,
  } = useWebSocket();

  // Scroll to bottom when messages change (only for new messages, not when loading history)
  useEffect(() => {
    if (!isLoadingHistory) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isLoadingHistory]);

  // Infinite scroll handler
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const container = e.currentTarget;
      const scrollTop = container.scrollTop;
      const scrollThreshold = 100;

      if (scrollTop <= scrollThreshold && hasMoreHistory && !isLoadingHistory) {
        console.log('Loading more history due to scroll');
        loadMoreHistory();
      }
    },
    [hasMoreHistory, isLoadingHistory, loadMoreHistory]
  );

  useEffect(() => {
    const latestBubblesMessage = [...messages]
      .reverse()
      .find((message) => message.type === 'bubbles');

    if (latestBubblesMessage && latestBubblesMessage.type === 'bubbles') {
      const bubblesData = latestBubblesMessage.payload?.data?.suggestions;
      if (Array.isArray(bubblesData)) {
        setCurrentBubbles(bubblesData);
      }
    } else {
      setCurrentBubbles([]);
    }
  }, [messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      sendMessage(input);
      setInput('');
      setCurrentBubbles([]);
    }
  };

  const handleBubbleClick = (suggestion: string) => {
    sendMessage(suggestion);
    setCurrentBubbles([]);
  };

  const handleOpenAuthModal = () => {
    setIsAuthModalOpen(true);
  };

  return (
    <>
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialFlowType="login"
      />

      <div className="flex flex-col h-full">
        <div
          ref={messagesContainerRef}
          className="flex-grow overflow-y-auto px-2 sm:px-4 py-2 flex justify-center"
          onScroll={handleScroll}
        >
          <div className="space-y-4 w-full max-w-[870px]">
            {/* Loading indicator for history */}
            {isLoadingHistory && (
              <div className="flex justify-center py-4">
                <div className="flex items-center space-x-2 text-gray-500">
                  <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                  <span className="text-sm">Loading older messages...</span>
                </div>
              </div>
            )}
            {messages.length === 0 ? (
              <>
                <div className="flex items-center justify-center h-full min-h-[300px]">
                  <div className="text-center">
                    <div className="flex justify-center mb-4">
                      <Image src="/star-logo.svg" alt="Star Logo" width={32} height={32} />
                    </div>
                    <h3 className="text-lg font-medium">Meet Klub AI</h3>
                    <p className="text-gray-500 mt-2">
                      Your go-to guide for all things funding. Get instant answers to any of your
                      funding related questions.
                    </p>
                    {!isReady && (
                      <p className="text-amber-500 mt-2">Initializing chat service...</p>
                    )}
                    {isReady && !isConnected && (
                      <p className="text-amber-500 mt-2">Connecting to chat service...</p>
                    )}

                    {/* Show bubbles when no messages */}
                    {currentBubbles.length > 0 && (
                      <div className="mt-6">
                        <Bubbles suggestions={currentBubbles} onBubbleClick={handleBubbleClick} />
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <>
                {messages.map((message, index) => {
                  return (
                    <div key={index}>
                      <ChatMessage
                        message={message}
                        isUser={message.role === 'user'}
                        timestamp={message.timestamp}
                        onOpenAuthModal={handleOpenAuthModal}
                        onSendEscalationRequest={sendEscalationRequest}
                      />
                    </div>
                  );
                })}

                {/* Show bubbles after messages if available */}
                {currentBubbles.length > 0 && (
                  <div className="flex justify-center my-4">
                    <Bubbles suggestions={currentBubbles} onBubbleClick={handleBubbleClick} />
                  </div>
                )}
              </>
            )}

            <div ref={messagesEndRef} />
          </div>
        </div>

        <div className="flex-shrink-0 p-2 sm:p-4 bg-white flex justify-center">
          <div className="flex items-center pb-[15.5px] pt-[15.5px] w-full max-w-[870px]">
            <form onSubmit={handleSubmit} className="relative w-full">
              <div className="relative flex items-center bg-white bg-opacity-60 border border-[#DFE1E4] rounded-md h-[53px] ">
                <Textarea
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Ask anything related about funding"
                  className="resize-none flex-grow w-full border-none py-[15.5px] px-[14px] focus-visible:ring-0 focus-visible:ring-offset-0 text-base text-[rgba(29,29,27,0.8)] font-normal"
                  rows={1}
                  disabled={!isConnected || !isReady}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit(e);
                    }
                  }}
                />
                <Button
                  type="submit"
                  size="icon"
                  disabled={isLoading || !input.trim() || !isConnected || !isReady}
                  className="flex items-center justify-center w-[34px] h-[34px] bg-[#9B81F5] hover:bg-[#8A70E4] rounded-[30px] mr-2"
                >
                  <Image src="/send-button.svg" alt="Send" width={34} height={34} />
                </Button>
              </div>
            </form>
            {isLoading && (
              <div className="text-xs text-gray-500 mt-3 absolute bottom-[-20px] pb-[15.5px]">
                Klub AI is typing...
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
