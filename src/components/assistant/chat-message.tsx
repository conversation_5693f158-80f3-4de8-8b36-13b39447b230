'use client';

import Image from 'next/image';
import { ActionCard } from './action-card';
import {
  ChatMessage as ChatMessageType,
  ActionMessage,
  ActionRequestMessage,
  BubblesMessage,
  SessionStartResponseMessage,
  SystemMessage,
  AgentSuggestionMessage,
  TextResponseMessage,
} from '@/hooks/use-websocket';
import { AlertCircle } from 'lucide-react';

interface ChatMessageProps {
  message: ChatMessageType;
  isUser: boolean;
  timestamp?: Date;
}

export function ChatMessage({ message, isUser }: ChatMessageProps) {
  // Helper function to get message text from different message types
  const getMessageText = (msg: ChatMessageType): string => {
    switch (msg.type) {
      case 'text_req':
        return msg.payload?.message || '';
      case 'text_resp':
        return (msg as TextResponseMessage).payload?.message || '';
      case 'action':
        return (msg as ActionMessage).message || '';
      case 'action_req':
        return (msg as ActionRequestMessage).payload?.message || '';
      case 'bubbles':
        return (msg as BubblesMessage).payload?.message || '';
      case 'session_start_resp':
        return (msg as SessionStartResponseMessage).payload?.message || '';
      case 'system':
        return (msg as SystemMessage).payload?.message || '';
      case 'agent_suggestion':
        return (msg as AgentSuggestionMessage).payload?.suggestion || '';
      default:
        return '';
    }
  };

  const messageText = getMessageText(message);
  const isErrorMessage = messageText.toLowerCase().startsWith('error:');

  // Helper function to get matched files data for text_resp messages
  const getMatchedFilesData = (msg: ChatMessageType) => {
    if (msg.type === 'text_resp') {
      const textRespMsg = msg as TextResponseMessage;
      return textRespMsg.payload?.data?.matched_files || null;
    }
    return null;
  };

  const matchedFiles = getMatchedFilesData(message);

  return (
    <div className="flex w-[870px] items-start gap-[20px]">
      <div className="w-[27px] h-[27px] flex-shrink-0">
        {!isUser && <Image src="/ai-logo.svg" alt="Klub AI" width={27} height={27} />}
        {isUser && (
          <Image src="/user-logo.svg" alt="Klub AI" width={27} height={27} />
          // <div className="w-full h-full rounded-full bg-[rgba(223,54,12,0.8)] flex items-center justify-center text-white">
          //   {getUserInitials()}
          // </div>
        )}
      </div>

      {/* Message Content */}
      <div className="w-full space-y-6">
        {message.type === 'text_req' ||
        message.type === 'text_resp' ||
        message.type === 'session_start_resp' ||
        message.type === 'agent_suggestion' ? (
          isErrorMessage ? (
            <div className="rounded-lg px-4 py-3 bg-red-50 border border-red-200 text-red-800 flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Sorry, something went wrong</p>
                <p className="text-sm mt-1">
                  We&apos;re experiencing technical difficulties. Please try again later or contact
                  support if the problem persists.
                </p>
              </div>
            </div>
          ) : (
            <div
              className={`w-full dm-sans ${
                message.type === 'agent_suggestion'
                  ? 'bg-green-50 border border-green-200 rounded-lg px-4 py-3'
                  : message.type === 'session_start_resp'
                    ? 'bg-blue-50 border border-blue-200 rounded-lg px-4 py-3'
                    : ''
              }`}
              style={{
                minHeight: '46px',
                fontStyle: 'normal',
                fontFamily: 'DM Sans',
                fontWeight: 400,
                fontSize: '18px',
                lineHeight: '23px',
                color:
                  message.type === 'agent_suggestion'
                    ? '#059669'
                    : message.type === 'session_start_resp'
                      ? '#1E40AF'
                      : '#1D1D1B',
              }}
            >
              {message.type === 'agent_suggestion' && (
                <span className="text-sm font-medium text-green-600 block mb-1">
                  Agent Suggestion
                </span>
              )}
              {message.type === 'session_start_resp' && (
                <span className="text-sm font-medium text-blue-600 block mb-1">
                  Session Started
                </span>
              )}
              {messageText}

              {/* Show matched files data for text_resp messages */}
              {message.type === 'text_resp' && matchedFiles && matchedFiles.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="text-xs text-gray-500 mb-2">
                    Debug Info (will be removed later):
                  </div>
                  <div className="space-y-1">
                    {matchedFiles.slice(0, 3).map((file, index) => (
                      <div key={index} className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                        <div className="font-medium">
                          Confidence: {(file.confidence * 100).toFixed(1)}%
                        </div>
                        <div className="truncate" title={file.filename}>
                          File: {file.filename}
                        </div>
                      </div>
                    ))}
                    {matchedFiles.length > 3 && (
                      <div className="text-xs text-gray-500 italic">
                        ... and {matchedFiles.length - 3} more files
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )
        ) : (
          <div className="w-full space-y-6">
            {/* Display the message text if it exists in the action message */}
            {messageText && (
              <div
                className="w-full dm-sans"
                style={{
                  minHeight: '46px',
                  fontFamily: 'DM Sans',
                  fontStyle: 'normal',
                  fontWeight: 400,
                  fontSize: '18px',
                  lineHeight: '23px',
                  color: '#1D1D1B',
                }}
              >
                {messageText}
              </div>
            )}

            {/* Display the action card - TODO: Update for new ActionCard interface */}
            {message.type === 'action' && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-3 mt-4">
                <span className="text-sm font-medium text-blue-600 block mb-1">
                  Test Action (needs update)
                </span>
                <div className="text-blue-800">
                  {(message as ActionMessage).message || 'Action available'}
                </div>
              </div>
            )}

            {/* Display action request */}
            {message.type === 'action_req' && (
              <div className="mt-4">
                <ActionCard message={message as ActionRequestMessage} />
              </div>
            )}

            {/* Bubbles are now handled in ChatInterface component */}
            {message.type === 'bubbles' && (
              <div className="text-sm text-gray-500 italic">Suggestions available above</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
