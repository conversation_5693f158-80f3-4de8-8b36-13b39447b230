'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { MessageCircle } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';

interface DummyActionCardProps {
  onEscalateToHuman: () => void;
}

export function DummyActionCard({ onEscalateToHuman }: DummyActionCardProps) {
  const { isAuthenticated } = useAuth();
  const [isEscalationRequested, setIsEscalationRequested] = useState(false);

  if (!isAuthenticated) {
    return null;
  }

  const handleEscalateClick = () => {
    onEscalateToHuman();
    setIsEscalationRequested(true);
  };

  return (
    <div
      className="flex flex-col items-start gap-2.5"
      style={{
        width: '420px',
        height: '221px',
        background: 'rgba(224, 249, 241, 0.8)',
        borderRadius: '4px',
        padding: '30px 0px 30px 30px',
        flex: 'none',
        order: 0,
        flexGrow: 0,
      }}
    >
      <div
        className="flex flex-col items-start"
        style={{
          padding: '0px',
          gap: '15px',
          width: '364px',
          height: '161px',
          flex: 'none',
          order: 0,
          flexGrow: 0,
        }}
      >
        {/* Heading */}
        <h3
          style={{
            width: '291px',
            height: '23px',
            fontFamily: 'DM Sans',
            fontStyle: 'normal',
            fontWeight: 600,
            fontSize: '18px',
            lineHeight: '23px',
            display: 'flex',
            alignItems: 'center',
            color: '#1D1D1B',
            opacity: 0.8,
            flex: 'none',
            order: 0,
            flexGrow: 0,
          }}
        >
          Need Human Assistance?
        </h3>

        {/* Description */}
        <p
          style={{
            width: '364px',
            height: '63px',
            fontFamily: 'DM Sans',
            fontStyle: 'normal',
            fontWeight: 400,
            fontSize: '16px',
            lineHeight: '21px',
            display: 'flex',
            alignItems: 'center',
            color: 'rgba(29, 29, 27, 0.8)',
            opacity: 0.8,
            flex: 'none',
            order: 1,
            flexGrow: 0,
          }}
        >
          Connect with one of our human operators for personalized help with your funding needs.
        </p>

        {/* Button */}
        <Button
          onClick={handleEscalateClick}
          size="sm"
          disabled={isEscalationRequested}
          className={`${
            isEscalationRequested
              ? 'bg-green-500 hover:bg-green-500 cursor-not-allowed'
              : 'bg-[#9B81F5] hover:bg-[#8A70E4]'
          } text-white`}
        >
          <MessageCircle className="w-4 h-4 mr-2" />
          {isEscalationRequested ? 'Request Successful' : 'Talk to Human Operator'}
        </Button>
      </div>
    </div>
  );
}
