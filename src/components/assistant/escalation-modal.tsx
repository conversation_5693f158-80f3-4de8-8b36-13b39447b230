'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { X } from 'lucide-react';
import Image from 'next/image';

interface EscalationModalProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId: string | null;
  onSendEscalation: (message: string) => void;
}

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'operator';
  timestamp: Date;
}

export function EscalationModal({
  isOpen,
  onClose,
  sessionId,
  onSendEscalation,
}: EscalationModalProps) {
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Reset messages when modal opens
  useEffect(() => {
    if (isOpen) {
      setMessages([
        {
          id: '1',
          content: 'Hello! You have been connected to a human operator. How can I help you today?',
          sender: 'operator',
          timestamp: new Date(),
        },
      ]);
    }
  }, [isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      // Add user message to local state
      const userMessage: Message = {
        id: Date.now().toString(),
        content: input.trim(),
        sender: 'user',
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, userMessage]);
      setIsLoading(true);

      // Send escalation request
      onSendEscalation(input.trim());
      setInput('');

      // Simulate operator response after a delay
      setTimeout(() => {
        const operatorMessage: Message = {
          id: (Date.now() + 1).toString(),
          content:
            'Thank you for your message. I have received your escalation request and will assist you shortly.',
          sender: 'operator',
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, operatorMessage]);
        setIsLoading(false);
      }, 2000);
    }
  };

  const handleClose = () => {
    setMessages([]);
    setInput('');
    setIsLoading(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl h-[600px] flex flex-col p-0">
        <DialogHeader className="flex-shrink-0 p-6 pb-4 border-b">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl font-semibold">Human Operator Chat</DialogTitle>
              <p className="text-sm text-gray-500 mt-1">
                Connected to human support - Session: {sessionId?.slice(-8) || 'N/A'}
              </p>
            </div>
            <Button variant="ghost" size="icon" onClick={handleClose} className="h-8 w-8">
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        {/* Messages area */}
        <div className="flex-grow overflow-y-auto px-6 py-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg px-4 py-2 ${
                    message.sender === 'user'
                      ? 'bg-[#9B81F5] text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p
                    className={`text-xs mt-1 ${
                      message.sender === 'user' ? 'text-purple-100' : 'text-gray-500'
                    }`}
                  >
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-gray-100 rounded-lg px-4 py-2">
                  <p className="text-sm text-gray-500">Operator is typing...</p>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input area */}
        <div className="flex-shrink-0 p-6 pt-4 border-t">
          <form onSubmit={handleSubmit} className="relative">
            <div className="relative flex items-center bg-white border border-[#DFE1E4] rounded-md">
              <Textarea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Type your message to the operator..."
                className="resize-none flex-grow border-none py-3 px-4 focus-visible:ring-0 focus-visible:ring-offset-0 text-sm"
                rows={1}
                disabled={isLoading}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit(e);
                  }
                }}
              />
              <Button
                type="submit"
                size="icon"
                disabled={isLoading || !input.trim()}
                className="flex items-center justify-center w-8 h-8 bg-[#9B81F5] hover:bg-[#8A70E4] rounded-full mr-2"
              >
                <Image src="/send-button.svg" alt="Send" width={16} height={16} />
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
