'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/hooks/use-auth';
import {
  checkUserExists,
  getHydraRedirectUrl,
  generateOtp,
  verifyOtp,
  getAccessToken,
  setCookie,
  getCookie,
  EMAIL_COOKIE,
  FLOW_ID_COOKIE,
  FLOW_TYPE_COOKIE,
} from '@/lib/auth';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialFlowId?: string;
  initialFlowType?: 'login' | 'registration';
  initialEmail?: string;
  onSwitchToSignup?: (email: string) => void;
}

export function LoginModal({
  isOpen,
  onClose,
  initialFlowId,
  initialFlowType = 'login',
  initialEmail,
  onSwitchToSignup,
}: LoginModalProps) {
  const [email, setEmail] = useState(initialEmail || '');
  const [otp, setOtp] = useState('');
  const [isOtpSent, setIsOtpSent] = useState(!!initialFlowId);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [flowId, setFlowId] = useState(initialFlowId || '');
  const [flowType, setFlowType] = useState<'login' | 'registration'>(initialFlowType);
  const { login } = useAuth();
  const processedFlowRef = useRef<string | null>(null);
  const processedCodeRef = useRef<string | null>(null);

  // Check for flow ID or authorization code in URL parameters
  useEffect(() => {
    const checkUrlParams = async () => {
      if (!isOpen) return;

      try {
        // Check URL parameters
        const url = new URL(window.location.href);

        // Check for authorization code
        const code = url.searchParams.get('code');
        if (code) {
          if (processedCodeRef.current === code) {
            console.log(
              '[LoginModal] Authorization code already processed, skipping:',
              code.substring(0, 10) + '...'
            );
            return;
          }

          processedCodeRef.current = code;

          setIsLoading(true);
          setError('');

          try {
            // Exchange the code for tokens
            console.log('code coming here to get token');
            const tokenData = await getAccessToken(code);
            console.log('Token data:', tokenData);

            // Call the login function from auth context
            if (tokenData.access_token) {
              await login(tokenData.access_token);

              // Remove code from URL parameters
              const url = new URL(window.location.href);
              url.searchParams.delete('code');
              window.history.replaceState({}, '', url.toString());

              // Close the modal
              onClose();
              return;
            } else {
              throw new Error('No access token received');
            }
          } catch (error) {
            console.error('Error exchanging code for token:', error);
            const errorMessage =
              error instanceof Error ? error.message : 'Failed to complete authentication';
            setError(errorMessage);
          } finally {
            setIsLoading(false);
          }
          return;
        }

        // Check for flow ID
        const flowId = url.searchParams.get('flow') || url.searchParams.get('flowId');
        const flowType = url.searchParams.get('flowType') || initialFlowType;

        // If we have a flow ID from URL or props, use it
        if (flowId || initialFlowId) {
          const finalFlowId = flowId || initialFlowId || '';

          // Check if we've already processed this flow ID to prevent duplicates
          if (processedFlowRef.current === finalFlowId) {
            console.log('[LoginModal] Flow already processed, skipping:', finalFlowId);
            return;
          }

          const storedEmail = getCookie(EMAIL_COOKIE) || '';
          const finalEmail = initialEmail || storedEmail;
          const finalFlowType = (flowType || initialFlowType) as 'login' | 'registration';

          if (!finalEmail) {
            console.error('No email found in cookies or props');
            setError('Missing email information. Please try again.');
            return;
          }

          // Mark this flow as being processed
          processedFlowRef.current = finalFlowId;

          setIsLoading(true);
          setError('');
          setFlowId(finalFlowId);
          setEmail(finalEmail);
          setFlowType(finalFlowType);

          // Store values in cookies
          setCookie(FLOW_ID_COOKIE, finalFlowId);
          setCookie(FLOW_TYPE_COOKIE, finalFlowType);

          // Generate OTP for login flow (no name or phone number needed)
          await generateOtp({
            email: finalEmail,
            flowId: finalFlowId,
            flowType: finalFlowType,
            clientName: 'demand-app-client',
          });

          // OTP sent successfully
          setIsOtpSent(true);
        }
      } catch (err) {
        console.error('URL parameter processing error:', err);
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to process authentication parameters';
        setError(errorMessage);
        setIsOtpSent(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkUrlParams();
  }, [isOpen, initialFlowId, initialEmail, initialFlowType, login, onClose]);

  const handleSendOtp = async () => {
    // Validate email
    if (!email || !email.includes('@')) {
      setError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Check if user exists - only if we're not already in a specific flow
      // This prevents unnecessary API calls when the flow type is already determined
      let userFlowType = flowType;

      // Only check user existence if we're not already in a specific flow
      // or if the flow type doesn't match the email (e.g., user changed email)
      if (!flowId) {
        userFlowType = await checkUserExists(email);
        setFlowType(userFlowType);

        // If user doesn't exist and we have a switch to signup function, use it
        if (userFlowType === 'registration' && onSwitchToSignup) {
          setIsLoading(false);
          onSwitchToSignup(email);
          return;
        }
      }

      // Store email in cookie
      setCookie(EMAIL_COOKIE, email);

      // Get redirect URL from Hydra
      const redirectData = await getHydraRedirectUrl(userFlowType);
      console.log('Hydra redirect data:', redirectData);

      // If we get a redirectTo URL, redirect to it
      // The auth service will redirect back to our app with the flowId
      if (redirectData.redirectTo) {
        window.location.href = redirectData.redirectTo;
        return;
      } else if (redirectData.flowId) {
        // Store flow ID
        setFlowId(redirectData.flowId);
        setCookie(FLOW_ID_COOKIE, redirectData.flowId);
        setCookie(FLOW_TYPE_COOKIE, redirectData.flowType || userFlowType);

        // Generate OTP for login flow (no name or phone number needed)
        const otpResponse = await generateOtp({
          email,
          flowId: redirectData.flowId,
          flowType: redirectData.flowType || userFlowType,
          clientName: 'demand-app-client',
          // No name or phone number for login flow
        });

        console.log('OTP response:', otpResponse);

        // OTP sent successfully
        setIsOtpSent(true);
      } else {
        throw new Error('Failed to initialize authentication flow');
      }
    } catch (err) {
      console.error('Login error:', err);
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to send OTP. Please try again.';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOtp = async () => {
    if (!otp) {
      setError('Please enter the OTP');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Validate OTP format
      if (otp.length !== 6 || !/^\d+$/.test(otp)) {
        throw new Error('Please enter a valid 6-digit OTP');
      }
      onClose();

      // Verify OTP with the backend
      // For login flow, we only need email, not name or phone number
      const verifyResponse = await verifyOtp({
        code: otp,
        flowId: flowId || undefined,
        flowType: flowType,
        email: email,
        clientName: 'demand-app-client',
        // No name or phone number for login flow
      });

      console.log('Verify OTP response:', verifyResponse);

      // If we get a redirecting response, the verification is in progress
      // The verifyOtp function will handle the redirect
      if (verifyResponse.redirecting) {
        // The redirect is already happening, just return
        return;
      }

      // If we get a token directly, use it
      if (verifyResponse.token) {
        // Call the login function from auth context
        await login(verifyResponse.token);
        return;
      }

      throw new Error('Authentication failed. Please try logging in again.');
    } catch (error) {
      console.error('OTP verification failed:', error);
      const { logout } = await import('@/lib/auth');
      await logout();

      window.location.reload();
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    // Reset state when closing
    setEmail('');
    setOtp('');
    setIsOtpSent(false);
    setError('');
    setFlowId('');
    setFlowType('login');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>{isOtpSent ? 'Enter OTP' : 'Login to Klub'}</DialogTitle>
          <DialogDescription>
            {isOtpSent
              ? `We've sent a one-time password to ${email}`
              : flowType === 'login'
                ? 'Enter your email to receive a one-time password'
                : 'Enter your email to create an account'}
          </DialogDescription>
        </DialogHeader>

        {error && <p className="text-sm font-medium text-red-500">{error}</p>}

        {!isOtpSent ? (
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
            </div>
          </div>
        ) : (
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="otp">OTP</Label>
              <Input
                id="otp"
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength={6}
                value={otp}
                onChange={(e) => setOtp(e.target.value.replace(/[^0-9]/g, ''))}
                placeholder="123456"
                disabled={isLoading}
              />
            </div>
          </div>
        )}

        <DialogFooter>
          <Button
            onClick={isOtpSent ? handleVerifyOtp : handleSendOtp}
            disabled={isLoading}
            className="bg-[#01734e] hover:bg-[#015a3d] w-full"
          >
            {isLoading ? 'Processing...' : isOtpSent ? 'Verify OTP' : 'Send OTP'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
