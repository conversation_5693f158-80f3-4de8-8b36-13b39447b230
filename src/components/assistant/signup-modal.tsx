'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/hooks/use-auth';
import {
  checkUserExists,
  getHydraRedirectUrl,
  generateOtp,
  verifyOtp,
  getAccessToken,
  setCookie,
  getCookie,
  EMAIL_COOKIE,
  FLOW_ID_COOKIE,
  FLOW_TYPE_COOKIE,
  NAME_COOKIE,
  PHONE_NUMBER_COOKIE,
} from '@/lib/auth';

interface SignupModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialFlowId?: string;
  initialFlowType?: 'login' | 'registration';
  initialEmail?: string;
  onSwitchToLogin?: (email: string) => void;
}

export function SignupModal({
  isOpen,
  onClose,
  initialFlowId,
  initialFlowType = 'registration',
  initialEmail,
  onSwitchToLogin,
}: SignupModalProps) {
  const [email, setEmail] = useState(initialEmail || '');
  const [name, setName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [isOtpSent, setIsOtpSent] = useState(!!initialFlowId);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [flowId, setFlowId] = useState(initialFlowId || '');
  const [flowType, setFlowType] = useState<'login' | 'registration'>(initialFlowType);
  const { login } = useAuth();
  const processedFlowRef = useRef<string | null>(null);
  const processedCodeRef = useRef<string | null>(null);

  // Update email when initialEmail changes
  useEffect(() => {
    if (initialEmail) {
      setEmail(initialEmail);
    }
  }, [initialEmail]);

  // Check for flow ID or authorization code in URL parameters
  useEffect(() => {
    const checkUrlParams = async () => {
      if (!isOpen) return;

      try {
        // Check URL parameters
        const url = new URL(window.location.href);

        // Check for authorization code
        const code = url.searchParams.get('code');
        if (code) {
          if (processedCodeRef.current === code) {
            console.log(
              '[SignupModal] Authorization code already processed, skipping:',
              code.substring(0, 10) + '...'
            );
            return;
          }

          processedCodeRef.current = code;

          setIsLoading(true);
          setError('');

          try {
            // Exchange the code for tokens
            const tokenData = await getAccessToken(code);
            console.log('Token data:', tokenData);

            // Call the login function from auth context
            if (tokenData.access_token) {
              await login(tokenData.access_token);

              // Remove code from URL parameters
              const url = new URL(window.location.href);
              url.searchParams.delete('code');
              window.history.replaceState({}, '', url.toString());

              // Close the modal
              onClose();
              return;
            } else {
              throw new Error('No access token received');
            }
          } catch (error) {
            console.error('Error exchanging code for token:', error);
            const errorMessage =
              error instanceof Error ? error.message : 'Failed to complete authentication';
            setError(errorMessage);
          } finally {
            setIsLoading(false);
          }
          return;
        }

        // Check for flow ID
        const flowId = url.searchParams.get('flow') || url.searchParams.get('flowId');
        const flowType = url.searchParams.get('flowType') || initialFlowType;

        // If we have a flow ID from URL or props, use it
        if (flowId) {
          const finalFlowId = flowId || initialFlowId || '';

          // Check if we've already processed this flow ID to prevent duplicates
          if (processedFlowRef.current === finalFlowId) {
            console.log('[SignupModal] Flow already processed, skipping:', finalFlowId);
            return;
          }

          const storedEmail = getCookie(EMAIL_COOKIE) || '';
          const finalEmail = initialEmail || storedEmail;
          const finalFlowType = (flowType || initialFlowType) as 'login' | 'registration';

          if (!finalEmail) {
            console.error('No email found in cookies or props');
            setError('Missing email information. Please try again.');
            return;
          }

          // Mark this flow as being processed
          processedFlowRef.current = finalFlowId;

          setIsLoading(true);
          setError('');
          setFlowId(finalFlowId);
          setEmail(finalEmail);
          setFlowType(finalFlowType);

          // Store values in cookies
          setCookie(FLOW_ID_COOKIE, finalFlowId);
          setCookie(FLOW_TYPE_COOKIE, finalFlowType);

          // Store name and phone number in cookies if available
          const storedName = getCookie(NAME_COOKIE) || '';
          const storedPhone = getCookie(PHONE_NUMBER_COOKIE) || '';

          // Generate OTP with all required fields for registration
          await generateOtp({
            email: finalEmail,
            name: storedName,
            phoneNumber: storedPhone,
            flowId: finalFlowId,
            flowType: finalFlowType,
            clientName: 'demand-app-client',
          });

          // OTP sent successfully
          setIsOtpSent(true);
        }
      } catch (err) {
        console.error('URL parameter processing error:', err);
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to process authentication parameters';
        setError(errorMessage);
        setIsOtpSent(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkUrlParams();
  }, [isOpen, initialFlowId, initialEmail, initialFlowType, login, onClose]);

  const handleSendOtp = async () => {
    // Validate inputs
    if (!email || !email.includes('@')) {
      setError('Please enter a valid email address');
      return;
    }

    if (!name.trim()) {
      setError('Please enter your name');
      return;
    }

    if (!phoneNumber.trim()) {
      setError('Please enter your phone number');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Check if user already exists
      const userFlowType = await checkUserExists(email);

      // If user already exists and we have a switch to login function, use it
      if (userFlowType === 'login' && onSwitchToLogin) {
        onSwitchToLogin(email);
        return;
      }

      // Store user information in cookies
      setCookie(EMAIL_COOKIE, email);
      setCookie(NAME_COOKIE, name);
      setCookie(PHONE_NUMBER_COOKIE, phoneNumber);
      setCookie(FLOW_TYPE_COOKIE, 'registration');

      // Get redirect URL from Hydra
      const redirectData = await getHydraRedirectUrl('registration');
      console.log('Hydra redirect data:', redirectData);

      // If we get a redirectTo URL, redirect to it
      // The auth service will redirect back to our app with the flowId
      if (redirectData.redirectTo) {
        window.location.href = redirectData.redirectTo;
        return;
      } else if (redirectData.flowId) {
        // Store flow ID
        setFlowId(redirectData.flowId);
        setCookie(FLOW_ID_COOKIE, redirectData.flowId);
        setCookie(FLOW_TYPE_COOKIE, 'registration');

        // Generate OTP with all required fields for registration
        const otpResponse = await generateOtp({
          email,
          name,
          phoneNumber,
          flowId: redirectData.flowId,
          flowType: 'registration',
          clientName: 'demand-app-client',
        });

        console.log('OTP response:', otpResponse);

        // OTP sent successfully
        setIsOtpSent(true);
      } else {
        throw new Error('Failed to initialize registration flow');
      }
    } catch (err) {
      console.error('Registration error:', err);
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to send OTP. Please try again.';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOtp = async () => {
    if (!otp) {
      setError('Please enter the OTP');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Validate OTP format
      if (otp.length !== 6 || !/^\d+$/.test(otp)) {
        throw new Error('Please enter a valid 6-digit OTP');
      }
      onClose();

      const verifyResponse = await verifyOtp({
        code: otp,
        flowId: flowId || undefined,
        flowType: flowType,
        email: email,
        name: name,
        phoneNumber: phoneNumber,
        clientName: 'demand-app-client',
      });

      console.log('Verify OTP response:', verifyResponse);

      // If we get a redirecting response, the verification is in progress
      // The verifyOtp function will handle the redirect
      if (verifyResponse.redirecting) {
        // The redirect is already happening, just return
        return;
      }

      // If we get a token directly, use it
      if (verifyResponse.token) {
        // Call the login function from auth context
        await login(verifyResponse.token);
        return;
      }

      throw new Error('Authentication failed. Please try signing up again.');
    } catch (error) {
      console.error('OTP verification failed:', error);

      const { logout } = await import('@/lib/auth');
      await logout();

      window.location.reload();
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setEmail('');
    setName('');
    setPhoneNumber('');
    setOtp('');
    setIsOtpSent(false);
    setError('');
    setFlowId('');
    setFlowType('registration');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>{isOtpSent ? 'Enter OTP' : 'Create an Account'}</DialogTitle>
          <DialogDescription>
            {isOtpSent
              ? `We've sent a one-time password to ${email}`
              : 'Enter your details to create an account'}
          </DialogDescription>
        </DialogHeader>

        {error && <p className="text-sm font-medium text-red-500">{error}</p>}

        {!isOtpSent ? (
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="John Doe"
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phoneNumber">Phone</Label>
              <Input
                id="phoneNumber"
                type="tel"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                placeholder="+919234567891"
                disabled={isLoading}
              />
            </div>
          </div>
        ) : (
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="otp">OTP</Label>
              <Input
                id="otp"
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength={6}
                value={otp}
                onChange={(e) => setOtp(e.target.value.replace(/[^0-9]/g, ''))}
                placeholder="123456"
                disabled={isLoading}
              />
            </div>
          </div>
        )}

        <DialogFooter>
          <Button
            onClick={isOtpSent ? handleVerifyOtp : handleSendOtp}
            disabled={isLoading}
            className="bg-[#01734e] hover:bg-[#015a3d] w-full"
          >
            {isLoading ? 'Processing...' : isOtpSent ? 'Verify OTP' : 'Send OTP'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
