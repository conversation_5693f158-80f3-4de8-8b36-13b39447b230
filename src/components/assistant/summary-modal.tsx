'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { X, MessageCircle, User, Bot, Loader2 } from 'lucide-react';

interface SummaryModalProps {
  isOpen: boolean;
  onClose: () => void;
  apiEndpoint?: string;
  heading: string;
}

interface Conversation {
  id: string;
  message: string;
  sender: 'user' | 'assistant';
  timestamp: string;
}

export function SummaryModal({ isOpen, onClose, apiEndpoint, heading }: SummaryModalProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && apiEndpoint) {
      fetchConversations();
    }
  }, [isOpen, apiEndpoint]);

  const fetchConversations = async () => {
    if (!apiEndpoint) {
      setConversations([
        {
          id: '1',
          message: 'Hello, I need help with funding options for my startup.',
          sender: 'user',
          timestamp: '2025-01-02T10:30:00Z',
        },
        {
          id: '2',
          message:
            'I can help you explore various funding options. What type of business do you have?',
          sender: 'assistant',
          timestamp: '2025-01-02T10:31:00Z',
        },
        {
          id: '3',
          message: 'I have a tech startup focused on AI solutions for small businesses.',
          sender: 'user',
          timestamp: '2025-01-02T10:32:00Z',
        },
        {
          id: '4',
          message:
            'Great! For AI startups, you might consider venture capital, angel investors, or government grants. Would you like me to provide more details about any of these options?',
          sender: 'assistant',
          timestamp: '2025-01-02T10:33:00Z',
        },
        {
          id: '5',
          message: 'Yes, please tell me more about venture capital options.',
          sender: 'user',
          timestamp: '2025-01-02T10:34:00Z',
        },
      ]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(apiEndpoint);
      if (!response.ok) {
        throw new Error(`Failed to fetch conversations: ${response.statusText}`);
      }
      const data = await response.json();

      setConversations(data.conversations || data || []);
    } catch (err) {
      console.error('Error fetching conversations:', err);
      setError(err instanceof Error ? err.message : 'Failed to load conversations');

      setConversations([
        {
          id: 'error-1',
          message: 'Failed to load conversations from API. Showing sample data.',
          sender: 'assistant',
          timestamp: new Date().toISOString(),
        },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleString();
    } catch {
      return timestamp;
    }
  };

  const getSenderIcon = (sender: string) => {
    return sender === 'user' ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />;
  };

  const getSenderColor = (sender: string) => {
    return sender === 'user' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl h-[700px] flex flex-col p-0">
        <DialogHeader className="flex-shrink-0 p-6 pb-4 border-b">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl font-semibold flex items-center gap-2">
                <MessageCircle className="w-5 h-5" />
                {heading}
              </DialogTitle>
              <p className="text-sm text-gray-500 mt-1">Conversation summary and history</p>
            </div>
            <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        {/* Content area */}
        <div className="flex-grow overflow-y-auto px-6 py-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-gray-400" />
                <p className="text-gray-500">Loading conversations...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <p className="text-red-500 mb-2">Error loading conversations</p>
                <p className="text-sm text-gray-500">{error}</p>
                <Button onClick={fetchConversations} variant="outline" size="sm" className="mt-4">
                  Retry
                </Button>
              </div>
            </div>
          ) : conversations.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p className="text-gray-500">No conversations found</p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {conversations.map((conversation) => (
                <div
                  key={conversation.id}
                  className={`flex ${conversation.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] ${conversation.sender === 'user' ? 'order-2' : 'order-1'}`}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      <div
                        className={`w-6 h-6 rounded-full flex items-center justify-center ${getSenderColor(conversation.sender)}`}
                      >
                        {getSenderIcon(conversation.sender)}
                      </div>
                      <span className="text-xs text-gray-500 capitalize">
                        {conversation.sender}
                      </span>
                      <span className="text-xs text-gray-400">
                        {formatTimestamp(conversation.timestamp)}
                      </span>
                    </div>
                    <div
                      className={`rounded-lg px-4 py-2 ${
                        conversation.sender === 'user'
                          ? 'bg-[#9B81F5] text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p className="text-sm">{conversation.message}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex-shrink-0 p-6 pt-4 border-t">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-500">
              {conversations.length} conversation{conversations.length !== 1 ? 's' : ''} loaded
            </p>
            <Button onClick={onClose} variant="outline">
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
