'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';

export function DashboardMetrics() {
  // This would typically come from your API
  const metrics = [
    {
      title: 'Total Funding',
      value: '₹25,00,000',
      change: '+12%',
      trend: 'up',
      icon: DollarSign,
    },
    {
      title: 'Active Loans',
      value: '3',
      change: '0%',
      trend: 'neutral',
      icon: CheckCircle,
    },
    {
      title: 'Pending Applications',
      value: '2',
      change: '+1',
      trend: 'up',
      icon: Clock,
    },
    {
      title: 'Upcoming Payment',
      value: '₹1,25,000',
      change: 'Due in 7 days',
      trend: 'down',
      icon: AlertCircle,
    },
  ];

  return (
    <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
            <metric.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <p className="text-xs text-muted-foreground flex items-center mt-1">
              {metric.trend === 'up' && <ArrowUpRight className="mr-1 h-4 w-4 text-green-500" />}
              {metric.trend === 'down' && <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />}
              <span
                className={
                  metric.trend === 'up'
                    ? 'text-green-500'
                    : metric.trend === 'down'
                      ? 'text-red-500'
                      : ''
                }
              >
                {metric.change}
              </span>
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
