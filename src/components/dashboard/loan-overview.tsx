'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function LoanOverview() {
  // This would typically come from your API
  const loans = [
    {
      id: 'LOAN-001',
      amount: '₹10,00,000',
      disbursedDate: '2025-01-15',
      tenure: '12 months',
      remainingAmount: '₹7,50,000',
      nextPayment: {
        date: '2025-05-15',
        amount: '₹91,667',
      },
    },
    {
      id: 'LOAN-002',
      amount: '₹5,00,000',
      disbursedDate: '2025-02-10',
      tenure: '6 months',
      remainingAmount: '₹4,16,667',
      nextPayment: {
        date: '2025-05-10',
        amount: '₹83,333',
      },
    },
    {
      id: 'LOAN-003',
      amount: '₹15,00,000',
      disbursedDate: '2025-03-05',
      tenure: '24 months',
      remainingAmount: '₹14,37,500',
      nextPayment: {
        date: '2025-05-05',
        amount: '₹62,500',
      },
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Active Loans</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {loans.map((loan) => (
            <div key={loan.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-semibold">{loan.id}</h4>
                <span className="text-sm text-gray-500">Disbursed: {loan.disbursedDate}</span>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-500">Loan Amount</p>
                  <p className="font-medium">{loan.amount}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Tenure</p>
                  <p className="font-medium">{loan.tenure}</p>
                </div>
              </div>

              <div className="bg-gray-50 p-3 rounded-md">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-gray-500">Next Payment</p>
                    <p className="font-medium">{loan.nextPayment.amount}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">Due Date</p>
                    <p className="font-medium">{loan.nextPayment.date}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
