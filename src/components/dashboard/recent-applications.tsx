'use client';

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

export function RecentApplications() {
  // This would typically come from your API
  const applications = [
    {
      id: 'APP-001',
      amount: '₹10,00,000',
      purpose: 'Inventory Financing',
      status: 'Approved',
      date: '2025-04-15',
    },
    {
      id: 'APP-002',
      amount: '₹5,00,000',
      purpose: 'Working Capital',
      status: 'In Review',
      date: '2025-04-20',
    },
    {
      id: 'APP-003',
      amount: '₹15,00,000',
      purpose: 'Equipment Purchase',
      status: 'Pending',
      date: '2025-04-22',
    },
    {
      id: 'APP-004',
      amount: '₹8,00,000',
      purpose: 'Marketing Campaign',
      status: 'Declined',
      date: '2025-04-10',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Approved':
        return 'bg-green-100 text-green-800';
      case 'In Review':
        return 'bg-blue-100 text-blue-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Declined':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Applications</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Purpose</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {applications.map((app) => (
              <TableRow key={app.id}>
                <TableCell className="font-medium">{app.id}</TableCell>
                <TableCell>{app.amount}</TableCell>
                <TableCell>{app.purpose}</TableCell>
                <TableCell>
                  <Badge variant="outline" className={getStatusColor(app.status)}>
                    {app.status}
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
