'use client';

import { usePathname } from 'next/navigation';
import { MessageSquare, LayoutDashboard, User, ChevronLeft, Menu, X } from 'lucide-react';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/use-auth';
import { AuthModal } from '@/components/assistant/auth-modal';

export function Sidebar() {
  const pathname = usePathname();
  const [expanded, setExpanded] = useState(() => {
    // Check if we have a saved state in localStorage
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('sidebarExpanded');
      // Default to true if no saved state
      return savedState === null ? true : savedState === 'true';
    }
    return true;
  });
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const { user, isAuthenticated, isLoading, logout } = useAuth();

  // Close mobile menu when clicking outside or on navigation
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 640) {
        // sm breakpoint
        setMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Define navigation items based on authentication status
  const navigation = [
    {
      name: 'Assistant',
      href: '/assistant',
      icon: MessageSquare,
      current: pathname === '/assistant',
      requiresAuth: false,
    },
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
      current: pathname === '/dashboard',
      requiresAuth: true,
    },
    {
      name: 'Profile',
      href: '/profile',
      icon: User,
      current: pathname === '/profile',
      requiresAuth: true,
    },
  ];

  // Filter navigation items based on authentication status
  const filteredNavigation = navigation.filter(
    (item) => !item.requiresAuth || (item.requiresAuth && isAuthenticated)
  );

  return (
    <>
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialFlowType="login"
      />

      {/* Mobile Menu Button */}
      <div className="md:hidden fixed top-4 left-4 z-50">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="bg-white shadow-md border border-gray-200"
        >
          {mobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
        </Button>
      </div>

      {/* Mobile Overlay */}
      {mobileMenuOpen && (
        <div
          className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Mobile Sidebar */}
      <div
        className={`md:hidden fixed left-0 top-0 h-full w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out z-50 ${
          mobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="h-16 shrink-0 relative flex items-center px-4">
          <Image
            src="/klub-full-logo.svg"
            alt="Klub Logo"
            width={100}
            height={40}
            style={{ objectFit: 'contain' }}
          />
        </div>

        <nav className="flex flex-1 flex-col pt-5">
          <ul className="flex flex-1 flex-col gap-y-4 px-2">
            {filteredNavigation.map((item) => (
              <li key={item.name}>
                <div
                  onClick={() => {
                    window.location.href = item.href;
                    setMobileMenuOpen(false);
                  }}
                  className={`
                    flex items-center gap-x-3 rounded-md p-2 text-sm font-medium cursor-pointer
                    ${
                      item.current
                        ? 'bg-gray-100 text-primary'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-primary'
                    }
                  `}
                >
                  {item.name === 'Assistant' ? (
                    <Image src="/sidebar-ai-logo.svg" alt="AI Logo" width={24} height={24} />
                  ) : (
                    <item.icon
                      className={`h-5 w-5 ${item.current ? 'text-primary' : 'text-gray-400'}`}
                      aria-hidden="true"
                    />
                  )}
                  <span>{item.name === 'Assistant' ? 'Klub AI' : item.name}</span>
                </div>
              </li>
            ))}
          </ul>
        </nav>

        <div className="p-4">
          {isLoading ? (
            <Button variant="ghost" className="w-full flex items-center gap-2 justify-start">
              <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                <Image src="/profile.svg" alt="Profile" width={24} height={24} />
              </div>
              <div className="flex flex-col items-start text-sm">
                <span className="font-medium text-gray-400">Loading...</span>
              </div>
            </Button>
          ) : isAuthenticated ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="w-full flex items-center gap-2 justify-start">
                  <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                    <Image src="/profile.svg" alt="Profile" width={24} height={24} />
                  </div>
                  <div className="flex flex-col items-start text-sm">
                    <span className="font-medium">{user?.name}</span>
                    <span className="text-xs text-gray-500">{user?.email}</span>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="flex flex-col items-start p-[20px] gap-[10px] w-[253px] h-[155px] bg-white border-[0.8px] border-[rgba(29,29,27,0.06)] shadow-[2px_4px_4px_rgba(1,1,1,0.1)] rounded-[1px]"
                sideOffset={5}
              >
                <div className="flex items-center gap-[10px] w-[198px] h-[40px]">
                  <div className="w-[28px] h-[28px] rounded-full bg-[rgba(223,54,12,0.8)] flex items-center justify-center text-white">
                    {user?.name?.charAt(0) || 'U'}
                  </div>
                  <div className="flex flex-col w-[153px] h-[40px]">
                    <div className="font-normal text-[16px] leading-[20px] text-[#1D1D1B]">
                      {user?.name}
                    </div>
                    <div className="font-normal text-[12px] leading-[20px] text-[#1D1D1B]">
                      {user?.email}
                    </div>
                  </div>
                </div>

                <div className="w-[217px] h-0 border-t border-[#E3E5E8]"></div>

                <div className="font-normal text-[14px] leading-[20px] text-[#1D1D1B] w-[213px] h-[20px]">
                  SuperNova Brand
                </div>

                <div className="w-[217px] h-0 border-t border-[#E3E5E8]"></div>

                <DropdownMenuItem
                  className="font-medium text-[14px] leading-[20px] text-[#7E5DED] w-[213px] h-[20px] p-0 focus:bg-transparent focus:text-[#7E5DED] cursor-pointer"
                  onClick={async () => {
                    setMobileMenuOpen(false);
                    // Clear URL parameters first to prevent re-initialization on reload
                    if (typeof window !== 'undefined') {
                      const url = new URL(window.location.href);
                      url.searchParams.delete('flow');
                      url.searchParams.delete('flowId');
                      url.searchParams.delete('code');
                      url.searchParams.delete('flowType');
                      url.searchParams.delete('email');
                      url.searchParams.delete('scope');
                      url.searchParams.delete('state');
                      window.history.replaceState({}, '', url.toString());
                    }

                    try {
                      // Import auth functions
                      const { logoutFromHydra, clearAllCookies } = await import('@/lib/auth');

                      // First call Hydra's logout endpoint to properly revoke the session
                      console.log('Calling Hydra logout endpoint from sidebar...');
                      await logoutFromHydra();

                      // Then clear all cookies
                      clearAllCookies();
                      console.log('All cookies cleared from sidebar logout');

                      // Call logout function to clear localStorage and other state
                      logout();

                      // Reload the page without URL parameters
                      window.location.reload();
                    } catch (error) {
                      console.error('Error during logout from sidebar:', error);

                      // Fallback: still try to clear cookies and reload
                      import('@/lib/auth').then(({ clearAllCookies }) => {
                        clearAllCookies();
                        logout();
                        window.location.reload();
                      });
                    }
                  }}
                >
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="w-full flex items-center gap-2 justify-start">
                  <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                    <Image src="/profile.svg" alt="Profile" width={24} height={24} />
                  </div>
                  <span className="text-sm">Guest User</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="flex flex-col items-start p-[20px] gap-[10px] w-[253px] bg-white border-[0.8px] border-[rgba(29,29,27,0.06)] shadow-[2px_4px_4px_rgba(1,1,1,0.1)] rounded-[1px]"
                sideOffset={5}
              >
                <div className="flex items-center gap-[10px] w-[198px] h-[40px]">
                  <div className="w-[35px] h-[35px] rounded-full bg-[rgba(223,54,12,0.8)] flex items-center justify-center text-white">
                    G
                  </div>
                  <div className="flex flex-col w-[153px] h-[40px]">
                    <div className="font-normal text-[16px] leading-[20px] text-[#1D1D1B]">
                      Guest User
                    </div>
                  </div>
                </div>

                <div className="w-[217px] h-0 border-t border-[#E3E5E8]"></div>

                <DropdownMenuItem
                  className="font-medium text-[14px] leading-[20px] text-[#7E5DED] w-[213px] h-[20px] p-0 focus:bg-transparent focus:text-[#7E5DED] cursor-pointer"
                  onClick={() => {
                    setMobileMenuOpen(false);
                    // Open the auth modal
                    setIsAuthModalOpen(true);
                  }}
                >
                  Sign In
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>

      {/* Desktop Sidebar */}
      <div
        className={`hidden md:flex h-full flex-col bg-white transition-all duration-300 ${
          expanded
            ? 'w-64 border-r border-gray-200'
            : 'w-[61px] border-r border-[rgba(29,29,27,0.06)] filter drop-shadow-[2px_4px_4px_rgba(1,1,1,0.1)]'
        }`}
      >
        <div className="h-16 shrink-0 relative">
          <div
            className="absolute left-0 top-0 h-full flex items-center px-4 cursor-pointer"
            onClick={() => {
              if (!expanded) {
                setExpanded(true);
                localStorage.setItem('sidebarExpanded', 'true');
              }
            }}
          >
            {expanded ? (
              <Image
                src="/klub-full-logo.svg"
                alt="Klub Logo"
                width={100}
                height={40}
                style={{ objectFit: 'contain' }}
              />
            ) : (
              <Image
                src="/klub-k-logo.svg"
                alt="Klub Logo"
                width={21}
                height={29}
                style={{
                  position: 'absolute',
                  left: '20px',
                  top: '34px',
                  objectFit: 'contain',
                }}
              />
            )}
          </div>

          {expanded && (
            <div className="absolute right-2 top-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setExpanded(false);
                  localStorage.setItem('sidebarExpanded', 'false');
                }}
              >
                <ChevronLeft size={18} />
              </Button>
            </div>
          )}
        </div>

        <nav className="flex flex-1 flex-col pt-5">
          <ul className="flex flex-1 flex-col gap-y-4 px-2">
            {filteredNavigation.map((item) => (
              <li key={item.name}>
                <div
                  onClick={() => {
                    window.location.href = item.href;
                  }}
                  className={`
                    flex items-center gap-x-3 rounded-md p-2 text-sm font-medium cursor-pointer
                    ${expanded ? '' : 'justify-center'}
                    ${
                      item.current
                        ? 'bg-gray-100 text-primary'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-primary'
                    }
                  `}
                  title={!expanded ? item.name : undefined}
                >
                  {item.name === 'Assistant' ? (
                    <Image src="/sidebar-ai-logo.svg" alt="AI Logo" width={24} height={24} />
                  ) : (
                    <item.icon
                      className={`h-5 w-5 ${item.current ? 'text-primary' : 'text-gray-400'}`}
                      aria-hidden="true"
                    />
                  )}
                  {item.name === 'Assistant'
                    ? expanded && <span>Klub AI</span>
                    : expanded && <span>{item.name}</span>}
                </div>
              </li>
            ))}
          </ul>
        </nav>

        <div className="p-4">
          {isLoading ? (
            // Loading state - show only the icon without any text
            <Button
              variant="ghost"
              className={`w-full flex items-center gap-2 ${expanded ? 'justify-start' : 'justify-center'}`}
            >
              <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                <Image src="/profile.svg" alt="Profile" width={24} height={24} />
              </div>
              {expanded && (
                <div className="flex flex-col items-start text-sm">
                  <span className="font-medium text-gray-400">Loading...</span>
                </div>
              )}
            </Button>
          ) : isAuthenticated ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className={`w-full flex items-center gap-2 ${expanded ? 'justify-start' : 'justify-center'}`}
                >
                  <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                    <Image src="/profile.svg" alt="Profile" width={24} height={24} />
                  </div>
                  {expanded && (
                    <div className="flex flex-col items-start text-sm">
                      <span className="font-medium">{user?.name}</span>
                      <span className="text-xs text-gray-500">{user?.email}</span>
                    </div>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="flex flex-col items-start p-[20px] gap-[10px] ml-8 w-[253px] h-[155px] bg-white border-[0.8px] border-[rgba(29,29,27,0.06)] shadow-[2px_4px_4px_rgba(1,1,1,0.1)] rounded-[1px]"
                sideOffset={5}
                style={{
                  left: expanded ? 'auto' : '57px',
                }}
              >
                <div className="flex items-center gap-[10px] w-[198px] h-[40px]">
                  <div className="w-[28px] h-[28px] rounded-full bg-[rgba(223,54,12,0.8)] flex items-center justify-center text-white">
                    {user?.name?.charAt(0) || 'U'}
                  </div>
                  <div className="flex flex-col w-[153px] h-[40px]">
                    <div className="font-normal text-[16px] leading-[20px] text-[#1D1D1B]">
                      {user?.name}
                    </div>
                    <div className="font-normal text-[12px] leading-[20px] text-[#1D1D1B]">
                      {user?.email}
                    </div>
                  </div>
                </div>

                <div className="w-[217px] h-0 border-t border-[#E3E5E8]"></div>

                <div className="font-normal text-[14px] leading-[20px] text-[#1D1D1B] w-[213px] h-[20px]">
                  SuperNova Brand
                </div>

                <div className="w-[217px] h-0 border-t border-[#E3E5E8]"></div>

                <DropdownMenuItem
                  className="font-medium text-[14px] leading-[20px] text-[#7E5DED] w-[213px] h-[20px] p-0 focus:bg-transparent focus:text-[#7E5DED] cursor-pointer"
                  onClick={async () => {
                    // Clear URL parameters first to prevent re-initialization on reload
                    if (typeof window !== 'undefined') {
                      const url = new URL(window.location.href);
                      url.searchParams.delete('flow');
                      url.searchParams.delete('flowId');
                      url.searchParams.delete('code');
                      url.searchParams.delete('flowType');
                      url.searchParams.delete('email');
                      url.searchParams.delete('scope');
                      url.searchParams.delete('state');
                      window.history.replaceState({}, '', url.toString());
                    }

                    try {
                      // Import auth functions
                      const { logoutFromHydra, clearAllCookies } = await import('@/lib/auth');

                      // First call Hydra's logout endpoint to properly revoke the session
                      console.log('Calling Hydra logout endpoint from sidebar...');
                      await logoutFromHydra();

                      // Then clear all cookies
                      clearAllCookies();
                      console.log('All cookies cleared from sidebar logout');

                      // Call logout function to clear localStorage and other state
                      logout();

                      // Reload the page without URL parameters
                      window.location.reload();
                    } catch (error) {
                      console.error('Error during logout from sidebar:', error);

                      // Fallback: still try to clear cookies and reload
                      import('@/lib/auth').then(({ clearAllCookies }) => {
                        clearAllCookies();
                        logout();
                        window.location.reload();
                      });
                    }
                  }}
                >
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className={`w-full flex items-center gap-2 ${expanded ? 'justify-start' : 'justify-center'}`}
                >
                  <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                    <Image src="/profile.svg" alt="Profile" width={24} height={24} />
                  </div>
                  {expanded && <span className="text-sm">Guest User</span>}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="flex flex-col items-start p-[20px] gap-[10px] ml-8 w-[253px] bg-white border-[0.8px] border-[rgba(29,29,27,0.06)] shadow-[2px_4px_4px_rgba(1,1,1,0.1)] rounded-[1px]"
                sideOffset={5}
                style={{
                  left: expanded ? 'auto' : '57px',
                }}
              >
                <div className="flex items-center gap-[10px] w-[198px] h-[40px]">
                  <div className="w-[35px] h-[35px] rounded-full bg-[rgba(223,54,12,0.8)] flex items-center justify-center text-white">
                    G
                  </div>
                  <div className="flex flex-col w-[153px] h-[40px]">
                    <div className="font-normal text-[16px] leading-[20px] text-[#1D1D1B]">
                      Guest User
                    </div>
                  </div>
                </div>

                <div className="w-[217px] h-0 border-t border-[#E3E5E8]"></div>

                <DropdownMenuItem
                  className="font-medium text-[14px] leading-[20px] text-[#7E5DED] w-[213px] h-[20px] p-0 focus:bg-transparent focus:text-[#7E5DED] cursor-pointer"
                  onClick={() => {
                    // Open the auth modal
                    setIsAuthModalOpen(true);
                  }}
                >
                  Sign In
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </>
  );
}
