'use client';

import { useBankAccounts } from '@/hooks/use-bank-accounts';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

export function BankAccounts() {
  const { bankAccounts, isLoading, error } = useBankAccounts();

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="space-y-4">
          {[1, 2].map((i) => (
            <div key={i} className="border-t border-gray-100 py-4 bg-gray-50">
              <Skeleton className="h-6 w-[200px] mx-4" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="text-center py-6 text-gray-500 dm-sans">No bank accounts found.</div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {bankAccounts.length === 0 ? (
        <div className="text-center py-6 text-gray-500 dm-sans">No bank accounts found.</div>
      ) : (
        <Accordion
          type="single"
          collapsible
          className="flex flex-col items-start p-0 gap-3 w-full overflow-visible"
        >
          {bankAccounts.map((account) => (
            <AccordionItem
              key={account.id}
              value={account.id}
              className="border-t border-gray-100 last:border-b w-full bg-gray-50"
            >
              <AccordionTrigger className="py-4 px-4 dm-sans font-medium text-base hover:no-underline bg-transparent">
                {account.bankName}
              </AccordionTrigger>
              <AccordionContent className="dm-sans border-t border-gray-100 bg-white">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-y-6 py-6 px-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-2">Account Number</div>
                    <div className="font-medium">{account.accountNumber}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-2">Statement Password</div>
                    <div className="font-medium">-</div>
                  </div>
                  <div className="md:col-span-3">
                    <div className="text-sm text-gray-500 mb-2">Statement Files</div>
                    <div className="flex items-center text-blue-600">
                      <svg
                        className="w-4 h-4 mr-2"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M7 10L12 15L17 10"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M12 15V3"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      No files uploaded
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}
    </div>
  );
}
