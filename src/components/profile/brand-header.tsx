'use client';

import { useEffect, useState } from 'react';

export function BrandHeader() {
  const [brandName, setBrandName] = useState<string>('');

  useEffect(() => {
    // Get brand name from localStorage
    const storedBrandName = localStorage.getItem('brandName');
    if (storedBrandName) {
      setBrandName(storedBrandName);
    }
  }, []);

  return <h1 className="text-xl font-semibold dm-sans">{brandName}</h1>;
}

export function BrandTitle() {
  const [brandName, setBrandName] = useState<string>('');

  useEffect(() => {
    // Get brand name from localStorage
    const storedBrandName = localStorage.getItem('brandName');
    if (storedBrandName) {
      setBrandName(storedBrandName);
    }
  }, []);

  return <h2 className="text-xl font-semibold dm-sans">{brandName}</h2>;
}
