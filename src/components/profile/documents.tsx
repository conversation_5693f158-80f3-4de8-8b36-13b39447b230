'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download, FileText } from 'lucide-react';
import { useDocuments, Document } from '@/hooks/use-documents';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

// Helper function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return bytes + ' B';
  else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
  else if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  else return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
};

// Helper function to format document type for display
const formatDocumentType = (type: string): string => {
  return type
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

export function Documents() {
  const { documents, isLoading, error, getDocumentDownloadUrl } = useDocuments();
  const [downloadingIds, setDownloadingIds] = useState<Set<string>>(new Set());

  // Group documents by document type
  const documentsByType = documents.reduce(
    (acc, doc) => {
      const type = doc.documentType;
      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push(doc);
      return acc;
    },
    {} as Record<string, Document[]>
  );

  const handleDownload = async (doc: Document) => {
    try {
      setDownloadingIds((prev) => new Set(prev).add(doc.id));
      const downloadUrl = await getDocumentDownloadUrl(doc.id);

      // Create a temporary link and trigger download
      const link = window.document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', doc.metadata.originalFilename);
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading document:', error);
      alert('Failed to download document. Please try again.');
    } finally {
      setDownloadingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(doc.id);
        return newSet;
      });
    }
  };

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium dm-sans">Documents</h2>
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="border rounded-md p-4">
              <Skeleton className="h-6 w-[200px] mb-2" />
              <Skeleton className="h-4 w-[300px]" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="text-center py-6 text-gray-500 dm-sans">No documents found.</div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4"></div>

      {Object.keys(documentsByType).length === 0 ? (
        <div className="text-center py-6 text-gray-500 dm-sans">No documents found.</div>
      ) : (
        <Accordion
          type="multiple"
          defaultValue={[]}
          className="flex flex-col items-start p-0 gap-3 w-full overflow-visible"
        >
          {Object.entries(documentsByType).map(([type, docs]) => (
            <AccordionItem
              key={type}
              value={type}
              className="border-t border-gray-100 last:border-b w-full bg-gray-50"
            >
              <AccordionTrigger className="py-4 px-4 dm-sans font-medium text-base hover:no-underline bg-transparent">
                {formatDocumentType(type)} ({docs.length})
              </AccordionTrigger>
              <AccordionContent className="dm-sans border-t border-gray-100 bg-white">
                <div className="space-y-3 py-4 px-4">
                  {docs.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between p-3 border border-gray-200 rounded-md"
                    >
                      <div className="flex items-center gap-3">
                        <FileText className="h-5 w-5 text-gray-400" />
                        <div>
                          <div className="font-medium">{doc.metadata.originalFilename}</div>
                          <div className="text-sm text-gray-500">
                            {formatFileSize(doc.metadata.size)}
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center gap-1 cursor-pointer"
                        onClick={() => handleDownload(doc)}
                        disabled={downloadingIds.has(doc.id)}
                      >
                        {downloadingIds.has(doc.id) ? (
                          'Downloading...'
                        ) : (
                          <>
                            <Download className="h-4 w-4" />
                            Download
                          </>
                        )}
                      </Button>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}
    </div>
  );
}
