'use client';

import { useBrandMembers } from '@/hooks/use-brand-members';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

export function MembersList() {
  const { members, isLoading, error } = useBrandMembers();

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="border-t border-gray-100 py-4">
              <Skeleton className="h-6 w-[200px]" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="text-center py-6 text-gray-500 dm-sans">No members found.</div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {members.length === 0 ? (
        <div className="text-center py-6 text-gray-500 dm-sans">No members found.</div>
      ) : (
        <Accordion
          type="single"
          collapsible
          className="flex flex-col items-start p-0 gap-3 w-full overflow-visible"
        >
          {members.map((member, index) => (
            <AccordionItem
              key={member.member.id}
              value={`item-${index}`}
              className="border-t border-gray-100 last:border-b w-full bg-gray-50"
            >
              <AccordionTrigger className="py-4 px-4 dm-sans font-medium text-base hover:no-underline bg-transparent">
                {member.member.metadata?.name || 'Team Member'}
              </AccordionTrigger>
              <AccordionContent className="dm-sans border-t border-gray-100 bg-white">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-y-6 py-6 px-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-2">Email ID</div>
                    <div className="font-medium break-words">
                      {member.member.metadata?.email || 'N/A'}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-2">Mobile Number</div>
                    <div className="font-medium">{member.member.metadata?.phone || 'N/A'}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-2">Role</div>
                    <div className="font-medium">{member.associationType || 'N/A'}</div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}
    </div>
  );
}
