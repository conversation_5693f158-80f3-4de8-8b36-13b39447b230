'use client';

import { useState, useEffect, createContext, useContext } from 'react';

// Define user type
interface User {
  id: string;
  name: string;
  role: string;
  avatar?: string;
  email?: string;
  phone?: string;
}

// Define auth context type
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (token: string) => Promise<void>;
  logout: () => Promise<void>;
}

// Create context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => {},
  logout: async () => {},
});

// Auth provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Function to update user from token
  const updateUserFromToken = async () => {
    try {
      // Import auth utilities dynamically to avoid SSR issues
      const { isAuthenticated, getUserInfo } = await import('@/lib/auth');

      if (isAuthenticated()) {
        try {
          // Get user info from ID token
          const userInfo = getUserInfo();
          console.log('User info:', userInfo);
          if (userInfo) {
            const userData: User = {
              id: userInfo.user_id as string,
              name: userInfo.name as string,
              role: 'User',
              email: userInfo?.email as string,
              phone: userInfo?.mobile as string,
            };
            setUser(userData);
          }
        } catch (error) {
          console.error('Auth token validation failed', error);
          localStorage.removeItem('auth_token');
        }
      } else {
        // If not authenticated, ensure user is null
        setUser(null);
      }
    } catch (error) {
      // Handle case where localStorage is not available (SSR)
      console.log('Auth check error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Check for token on initial load
  useEffect(() => {
    updateUserFromToken();
  }, []);

  // Listen for token refresh events
  useEffect(() => {
    const handleTokenRefresh = async () => {
      console.log('Token refresh event detected, updating user state');
      await updateUserFromToken();
    };

    // Import TOKEN_REFRESH_EVENT dynamically
    const importTokenRefreshEvent = async () => {
      const { TOKEN_REFRESH_EVENT } = await import('@/lib/auth');

      if (typeof window !== 'undefined') {
        window.addEventListener(TOKEN_REFRESH_EVENT, handleTokenRefresh);

        // Cleanup function
        return () => {
          window.removeEventListener(TOKEN_REFRESH_EVENT, handleTokenRefresh);
        };
      }
    };

    importTokenRefreshEvent();
  }, []);

  // Login function
  const login = async (token: string) => {
    setIsLoading(true);
    try {
      // Store the token in localStorage for compatibility with existing code
      localStorage.setItem('auth_token', token);

      // Import auth utilities dynamically to avoid SSR issues
      const { getUserInfo } = await import('@/lib/auth');

      // Get user info from ID token
      const userInfo = getUserInfo();
      if (userInfo) {
        const userData: User = {
          id: userInfo.user_id as string,
          name: userInfo.name as string,
          role: 'User',
          email: userInfo?.email as string,
          phone: userInfo?.mobile as string,
        };
        setUser(userData);
      } else {
        // Fallback if no user info is available
        const userData: User = { id: '1', name: 'User', role: 'User' };
        setUser(userData);
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Import auth utilities dynamically to avoid SSR issues
      const { logout: authLogout } = await import('@/lib/auth');

      // Call the auth logout function to clear cookies and revoke Hydra session
      await authLogout();

      // Clear localStorage
      localStorage.removeItem('auth_token');

      // Clear user state
      setUser(null);
    } catch (error) {
      console.error('Error during logout:', error);
      // Fallback if import fails
      localStorage.removeItem('auth_token');
      setUser(null);
    }
  };

  const contextValue = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
}

// Custom hook to use auth context
export const useAuth = () => useContext(AuthContext);
