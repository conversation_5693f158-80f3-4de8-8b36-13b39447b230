'use client';

import { useState, useEffect } from 'react';
import { useBrandMembers } from './use-brand-members';
import { apiClient } from '@/lib/api-client';

export interface BankAccount {
  id: string;
  borrowerId: string;
  countryCode: string;
  bankName: string;
  branchName: string;
  accountHolderName: string;
  accountNumber: string;
  accountType: string;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

interface BankAccountsResponse {
  code: number;
  data: BankAccount[];
  message: string;
  status: string;
}

export function useBankAccounts() {
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { brand } = useBrandMembers();

  // Store brand info in localStorage whenever it changes
  useEffect(() => {
    if (brand?.id) {
      localStorage.setItem('brandId', brand.id);
      localStorage.setItem('brandName', brand.businessName);
    }
  }, [brand]);

  useEffect(() => {
    const fetchBankAccounts = async () => {
      if (!brand?.id) {
        setIsLoading(false);
        return;
      }

      try {
        const brandId = brand.id;

        const data = await apiClient<BankAccountsResponse>(
          '/agent-svc/v1/bank-details/filter?demandCode=NOON',
          {
            method: 'POST',
            body: {
              filters: { borrower_id: brandId },
            },
          }
        );
        setBankAccounts(data.data || []);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching bank accounts:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch bank accounts'));
        setIsLoading(false);
      }
    };

    fetchBankAccounts();
  }, [brand]);

  return { bankAccounts, isLoading, error };
}
