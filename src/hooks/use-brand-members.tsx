'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './use-auth';
import { getBrandWithMembers, BrandMember, Brand } from '@/services/brand-service';
import { getUserInfo } from '@/lib/auth';

interface UseBrandMembersResult {
  members: BrandMember[];
  brand: Brand | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

export function useBrandMembers(): UseBrandMembersResult {
  const [members, setMembers] = useState<BrandMember[]>([]);
  const [brand, setBrand] = useState<Brand | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { isAuthenticated } = useAuth();

  const fetchBrandMembers = useCallback(async () => {
    if (!isAuthenticated) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Get customer ID from the ID token
      const userInfo = getUserInfo();
      if (!userInfo) {
        throw new Error('User information not available');
      }

      if (!userInfo?.user_id) {
        throw new Error('Customer ID not found in token');
      }

      const customerId = userInfo.user_id as string;
      const response = await getBrandWithMembers(customerId);

      setMembers(response.data.members);
      setBrand(response.data.brand);

      // Store the entire brand object in localStorage for future use
      localStorage.setItem('brandInfo', JSON.stringify(response.data.brand));
    } catch (err) {
      console.error('Error fetching brand members:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch brand members'));
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    fetchBrandMembers();
  }, [fetchBrandMembers]);

  return {
    members,
    brand,
    isLoading,
    error,
    refetch: fetchBrandMembers,
  };
}
