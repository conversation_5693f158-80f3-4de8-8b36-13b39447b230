'use client';

import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api-client';

export interface DocumentMetadata {
  category?: string;
  contentType: string;
  description: string;
  originalFilename: string;
  size: number;
  amount?: number;
}

export interface Document {
  id: string;
  documentKey: string;
  documentType: string;
  metadata: DocumentMetadata;
}

export interface DocumentDownloadInfo {
  url: string;
  documentKey: string;
  filename: string;
  expiresAt: string;
}

interface DocumentsResponse {
  code: number;
  data: {
    documents: Document[];
  };
  message: string;
  status: string;
}

interface DocumentDownloadResponse {
  code: number;
  data: DocumentDownloadInfo;
  message: string;
  status: string;
}

export function useDocuments() {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        // Get brand info from localStorage
        const brandJson = localStorage.getItem('brandInfo');
        if (!brandJson) {
          setIsLoading(false);
          return;
        }

        const brand = JSON.parse(brandJson);
        const brandCode = brand.brandCode;
        const demandCode = 'NOON'; // Fallback to NOON if not available

        const response = await apiClient<DocumentsResponse>(
          `/agent-svc/v1/documents/list?resourceCode=${brandCode}&brandCode=${brandCode}&demandCode=${demandCode}`
        );

        setDocuments(response.data.documents || []);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching documents:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch documents'));
        setIsLoading(false);
      }
    };

    fetchDocuments();
  }, []);

  const getDocumentDownloadUrl = async (documentId: string): Promise<string> => {
    try {
      // Get brand info from localStorage
      const brandJson = localStorage.getItem('brandInfo');
      if (!brandJson) {
        throw new Error('Brand information not available');
      }

      const demandCode = 'NOON'; // Fallback to NOON if not available

      const response = await apiClient<DocumentDownloadResponse>(
        `/agent-svc/v1/documents/${documentId}/download?demandCode=${demandCode}`
      );

      return response.data.url;
    } catch (err) {
      console.error('Error getting document download URL:', err);
      throw err instanceof Error ? err : new Error('Failed to get document download URL');
    }
  };

  return { documents, isLoading, error, getDocumentDownloadUrl };
}
