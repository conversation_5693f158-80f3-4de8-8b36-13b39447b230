'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { fetchConversations, parseConversationMessages } from '@/lib/conversations';

// Utility function to generate message IDs
function generateMessageId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

// Utility function to get current timestamp in ISO format
function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

// Session storage key
const SESSION_ID_KEY = 'demand_session_id';

// Define message types
export interface InitMessage {
  type: 'init';
  sender: 'system';
  session_id: string;
  payload: {
    session_id: string;
  };
  meta: {
    message_id: string;
    created_at: string;
    received_at?: string;
  };
}

export interface ErrorMessage {
  type: 'error';
  sender: 'system';
  session_id: string;
  payload: {
    code: string;
    message: string;
  };
  meta: {
    message_id: string;
    created_at: string;
    received_at?: string;
  };
}

export interface SessionStartMessage {
  type: 'session_start';
  sender: 'demand';
  session_id: string;
  payload: {
    is_guest: boolean;
    access_token?: string;
    id_token?: string;
  };
}

export interface TextMessage {
  type: 'text_req';
  sender: 'customer' | 'demand';
  session_id: string;
  payload: {
    message: string;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface MatchedFile {
  confidence: number;
  filename: string;
  filepath: string;
  source_id: string;
  title: string;
}

export interface TextResponseMessage {
  type: 'text_resp';
  sender: 'agent';
  session_id: string;
  payload: {
    message: string;
    data?: {
      matched_files?: MatchedFile[];
      total_matches?: number;
      [key: string]: unknown;
    };
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface ActionMessage {
  type: 'action';
  sender?: 'customer' | 'agent' | 'system';
  session_id?: string;
  category: string;
  message?: string; // Optional message to display with the action card
  title?: string; // Title for the action card
  email?: string; // Email for auth actions
  flowType?: 'login' | 'registration'; // Flow type for auth actions
  data?: unknown; // Data for form actions
  payload?: unknown;
  meta?: {
    message_id: string;
    timestamp: string;
  };
  // Add other fields that might be in action messages
  [key: string]: unknown;
}

export interface SystemMessage {
  type: 'system';
  sender: 'system';
  session_id: string;
  payload: {
    message: string;
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface AgentSuggestionMessage {
  type: 'agent_suggestion';
  sender: 'agent';
  session_id: string;
  payload: {
    suggestion: string;
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface EscalationMessage {
  type: 'escalation';
  sender: 'customer' | 'agent' | 'system';
  session_id: string;
  payload: {
    reason: string;
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface AuthMessage {
  type: 'auth';
  sender: 'customer' | 'system';
  session_id: string;
  payload: {
    action: 'login' | 'logout' | 'register' | 'verify';
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface ActionResponseMessage {
  type: 'action_resp';
  sender: 'customer';
  session_id: string;
  payload: {
    action_id: string;
    response: unknown;
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface FormField {
  id: string;
  type: 'text' | 'email' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'date';
  label: string;
  placeholder?: string;
  required?: boolean;
  value?: string | number | boolean;
  options?: Array<{ label: string; value: string | number }>;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

export interface ActionRequestMessage {
  type: 'action_req';
  sender: 'operator' | 'agent';
  session_id: string;
  payload: {
    category: 'summary' | 'form' | 'login' | 'offer' | 'content';
    message?: string;
    data: {
      heading: string;
      description: string;
      apiEndpoint?: string;
      url?: string;
      formFields?: FormField[];
      [key: string]: unknown;
    };
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    createdAt: string;
  };
}

export interface BubblesMessage {
  type: 'bubbles';
  sender: 'agent';
  session_id: string;
  payload: {
    message?: string;
    data?: {
      suggestions: string[];
      [key: string]: unknown;
    };
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export interface SessionStartResponseMessage {
  type: 'session_start_resp';
  sender: 'agent';
  session_id: string;
  payload: {
    message?: string;
    status: string;
    [key: string]: unknown;
  };
  meta: {
    message_id: string;
    timestamp: string;
  };
}

export type ChatMessage =
  | TextMessage
  | TextResponseMessage
  | ActionMessage
  | ActionRequestMessage
  | BubblesMessage
  | SessionStartResponseMessage
  | SystemMessage
  | AgentSuggestionMessage;
export type WebSocketMessage =
  | InitMessage
  | ErrorMessage
  | SessionStartMessage
  | TextMessage
  | TextResponseMessage
  | ActionMessage
  | ActionRequestMessage
  | BubblesMessage
  | SessionStartResponseMessage
  | SystemMessage
  | AgentSuggestionMessage
  | EscalationMessage
  | AuthMessage
  | ActionResponseMessage;

export function useWebSocket() {
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<
    Array<ChatMessage & { role: 'user' | 'assistant'; timestamp: Date }>
  >([]);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionStarted, setSessionStarted] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [hasMoreHistory, setHasMoreHistory] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const socketRef = useRef<WebSocket | null>(null);
  const sessionStartedRef = useRef(false);
  const sessionStartSentRef = useRef<string | null>(null); // Track which session ID we've sent session_start for

  // Function to get stored session ID from localStorage
  const getStoredSessionId = useCallback((): string | null => {
    try {
      return localStorage.getItem(SESSION_ID_KEY);
    } catch (error) {
      console.error('Error reading session ID from localStorage:', error);
      return null;
    }
  }, []);

  // Function to store session ID in localStorage
  const storeSessionId = useCallback((id: string) => {
    try {
      localStorage.setItem(SESSION_ID_KEY, id);
      setSessionId(id);
      console.log('Session ID stored:', id);
    } catch (error) {
      console.error('Error storing session ID in localStorage:', error);
    }
  }, []);

  // Function to clear session ID from localStorage
  const clearStoredSessionId = useCallback(() => {
    try {
      localStorage.removeItem(SESSION_ID_KEY);
      setSessionId(null);
      console.log('Session ID cleared from localStorage');
    } catch (error) {
      console.error('Error clearing session ID from localStorage:', error);
    }
  }, []);

  // Function to load existing conversations
  const loadConversations = useCallback(async (currentSessionId: string | null) => {
    if (!currentSessionId) {
      console.log('No session ID available, skipping conversation loading');
      return;
    }

    try {
      console.log('Loading conversations for session:', currentSessionId);
      const result = await fetchConversations(currentSessionId, { page: 1, pageSize: 50 });
      const parsedMessages = parseConversationMessages(result.conversations);

      setHasMoreHistory(result.hasMore);
      setCurrentPage(1);

      console.log('Loaded conversations:', result);
      console.log('Parsed messages:', parsedMessages);

      // Convert parsed messages to chat messages format
      const chatMessages = parsedMessages.map((msg) => ({
        type: msg.type,
        sender: msg.role === 'user' ? 'demand' : 'agent',
        session_id: currentSessionId,
        payload: {
          message: msg.content,
          ...msg.payload,
        },
        meta: {
          message_id: msg.id,
          timestamp: msg.timestamp.toISOString(),
          ...msg.meta,
        },
        role: msg.role,
        timestamp: msg.timestamp,
      }));

      setMessages(
        chatMessages as Array<ChatMessage & { role: 'user' | 'assistant'; timestamp: Date }>
      );
      console.log('Conversations loaded and set in state');
    } catch (error) {
      console.error('Error loading conversations:', error);
    }
  }, []);

  // Initialize session ID on component mount
  useEffect(() => {
    const storedSessionId = getStoredSessionId();
    if (storedSessionId) {
      setSessionId(storedSessionId);
      console.log('Loaded existing session ID from localStorage:', storedSessionId);
    }
  }, [getStoredSessionId]);

  // Connect to WebSocket
  useEffect(() => {
    // Close existing connection if any
    if (
      socketRef.current &&
      (socketRef.current.readyState === WebSocket.OPEN ||
        socketRef.current.readyState === WebSocket.CONNECTING)
    ) {
      socketRef.current.close();
    }

    const baseWsUrl = process.env.NEXT_PUBLIC_WEBSOCKET_URL;

    if (!baseWsUrl) {
      console.error('WebSocket URL not defined in environment variables');
      return;
    }

    // Determine WebSocket URL based on whether we have a session ID
    const storedSessionId = getStoredSessionId();
    const wsUrl =
      storedSessionId && storedSessionId !== 'undefined'
        ? `${baseWsUrl}demand/${storedSessionId}`
        : `${baseWsUrl}demand`;

    console.log(`Connecting to WebSocket at: ${wsUrl}`);

    const socket = new WebSocket(wsUrl);

    socket.onopen = () => {
      console.log('WebSocket connection established');
      setIsConnected(true);
      setSessionStarted(false);
      sessionStartedRef.current = false;
      sessionStartSentRef.current = null; // Reset session start tracking
      // Wait for init message before sending session_start
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('Received message:', data);

        // Handle init message - always comes first
        if (data.type === 'init') {
          const initMessage = data as InitMessage;
          console.log('Received init message with session ID:', initMessage.session_id);
          console.log('Current sessionStartSentRef:', sessionStartSentRef.current);
          console.log('Current sessionStartedRef:', sessionStartedRef.current);

          // Blindly store the new session ID
          storeSessionId(initMessage.session_id);

          // Send session_start message after receiving init
          setTimeout(async () => {
            console.log('Attempting to send session_start message...');

            if (!socket || socket.readyState !== WebSocket.OPEN) {
              console.log('Cannot send session start: socket not ready');
              return;
            }

            // Check if session is already started or if we've already sent session_start for this session ID
            if (
              sessionStartedRef.current ||
              sessionStartSentRef.current === initMessage.session_id
            ) {
              console.log(
                'Session already started or session_start already sent for this session ID, skipping'
              );
              return;
            }

            // Mark that we're sending session_start for this session ID
            sessionStartSentRef.current = initMessage.session_id;

            console.log('Proceeding with session_start message...');
            try {
              // Check for authentication tokens
              const { getCookie, ACCESS_TOKEN_COOKIE, ID_TOKEN_COOKIE } = await import(
                '@/lib/auth'
              );
              const accessToken = getCookie(ACCESS_TOKEN_COOKIE);
              const idToken = getCookie(ID_TOKEN_COOKIE);

              const isUserAuthenticated = !!(accessToken && idToken);

              const sessionStartMessage: SessionStartMessage = {
                type: 'session_start',
                sender: 'demand',
                session_id: initMessage.session_id,
                payload: {
                  is_guest: !isUserAuthenticated,
                  ...(isUserAuthenticated && { access_token: accessToken, id_token: idToken }),
                },
              };

              socket.send(JSON.stringify(sessionStartMessage));
              console.log('Session start message sent:', sessionStartMessage);
            } catch (error) {
              console.error('Error sending session start message:', error);
            }
          }, 100);
          return;
        }

        // Handle error messages
        if (data.type === 'error') {
          const errorMessage = data as ErrorMessage;
          console.error('Received error message:', errorMessage);

          // If it's an invalid_token error, clear session and close connection
          if (errorMessage.payload.code === 'invalid_token') {
            console.log('Invalid token error received, clearing session and closing connection');
            clearStoredSessionId();
            setSessionStarted(false);
            sessionStartedRef.current = false;
            socket.close();
            return;
          }
        }

        // Handle session_start response
        if (data.type === 'session_start_resp') {
          console.log('Session start acknowledged by server:', data);
          setSessionStarted(true);
          sessionStartedRef.current = true;
          console.log('Session is now active and ready for messages');

          loadConversations(sessionId);
        }

        if (data.type === 'bubbles') {
          console.log('Received bubbles message:', data);
          const bubblesData = data.payload?.data?.suggestions;
          if (Array.isArray(bubblesData)) {
            console.log('Extracted suggestions:', bubblesData);
          }
        }

        if (data.type === 'action_req') {
          console.log('Received action_req message:', data);
          const { category, data: actionData } = data.payload || {};
          console.log('Action category:', category);
          console.log('Action data:', actionData);
        }

        // Only add messages that should appear in chat
        // Show only: text_resp, action_req, bubbles
        // Exclude: system messages, init, error, session_start_resp and other internal message types
        if (data.type === 'text_resp' || data.type === 'action_req' || data.type === 'bubbles') {
          setMessages((prev) => [
            ...prev,
            {
              ...data,
              role: 'assistant',
              timestamp: new Date(),
            },
          ]);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    socket.onclose = () => {
      console.log('WebSocket connection closed');
      setIsConnected(false);
      setSessionStarted(false);
      sessionStartedRef.current = false;
    };

    socket.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    socketRef.current = socket;

    // Cleanup on unmount
    return () => {
      if (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING) {
        socket.close();
      }
    };
  }, [getStoredSessionId, storeSessionId, clearStoredSessionId, loadConversations, sessionId]);

  // We'll replace this with API calls in the future
  // No need to save messages to localStorage

  // Send message function
  const sendMessage = useCallback(
    (message: string) => {
      if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN || !sessionId) {
        console.error('WebSocket is not connected or session ID is missing');
        return;
      }

      if (!sessionStarted) {
        console.error('Session not started yet, cannot send message');
        return;
      }

      const messageObj: TextMessage = {
        type: 'text_req',
        sender: 'demand',
        session_id: sessionId,
        payload: {
          message: message,
        },
        meta: {
          message_id: generateMessageId(),
          timestamp: getCurrentTimestamp(),
        },
      };

      try {
        socketRef.current.send(JSON.stringify(messageObj));

        // Add user message to the messages array
        setMessages((prev) => [
          ...prev,
          {
            ...messageObj,
            role: 'user',
            timestamp: new Date(),
          },
        ]);

        setIsLoading(true);
      } catch (error) {
        console.error('Error sending message:', error);
      }
    },
    [sessionId, sessionStarted]
  );

  // Function to clear chat history
  const clearChatHistory = useCallback(() => {
    setMessages([]);
  }, []);

  // Function to send escalation request
  const sendEscalationRequest = useCallback(() => {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN || !sessionId) {
      console.error('WebSocket is not connected or session ID is missing');
      return;
    }

    const escalationMessage = {
      type: 'escalation_req',
      sessionId: sessionId,
      sender: 'demand',
      payload: {},
    };

    try {
      socketRef.current.send(JSON.stringify(escalationMessage));
      console.log('Escalation request sent:', escalationMessage);
    } catch (error) {
      console.error('Error sending escalation request:', error);
    }
  }, [sessionId]);

  const loadMoreHistory = useCallback(async () => {
    if (!sessionId || isLoadingHistory || !hasMoreHistory) {
      return;
    }

    setIsLoadingHistory(true);

    try {
      const nextPage = currentPage + 1;
      console.log('Loading more history, page:', nextPage);

      const result = await fetchConversations(sessionId, { page: nextPage, pageSize: 50 });
      const parsedMessages = parseConversationMessages(result.conversations);

      console.log('Loaded more conversations:', result);

      setHasMoreHistory(result.hasMore);
      setCurrentPage(nextPage);

      const chatMessages = parsedMessages.map((msg) => ({
        type: msg.type,
        sender: msg.role === 'user' ? 'demand' : 'agent',
        session_id: sessionId,
        payload: {
          message: msg.content,
          ...msg.payload,
        },
        meta: {
          message_id: msg.id,
          timestamp: msg.timestamp.toISOString(),
          ...msg.meta,
        },
        role: msg.role,
        timestamp: msg.timestamp,
      }));

      setMessages((prev) => [
        ...(chatMessages.reverse() as Array<
          ChatMessage & { role: 'user' | 'assistant'; timestamp: Date }
        >),
        ...prev,
      ]);

      console.log('More conversations loaded and prepended to state');
    } catch (error) {
      console.error('Error loading more conversations:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  }, [sessionId, isLoadingHistory, hasMoreHistory, currentPage]);

  return {
    isConnected,
    messages,
    sendMessage,
    sendEscalationRequest,
    isLoading,
    isReady: isConnected && !!sessionId && sessionStarted,
    clearChatHistory,
    sessionStarted,
    sessionId,
    loadMoreHistory,
    isLoadingHistory,
    hasMoreHistory,
  };
}
