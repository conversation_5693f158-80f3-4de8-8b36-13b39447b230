import { getCookie, ACCESS_TOKEN_COOKIE, refreshAccessToken } from './auth';

const API_BASE_URL = 'http://localhost:5000/api';

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  body?: any;
  headers?: Record<string, string>;
  requiresAuth?: boolean;
}

/**
 * Generic API client for making HTTP requests
 */
export async function apiClient<T>(endpoint: string, options: ApiOptions = {}): Promise<T> {
  const { method = 'GET', body, headers = {}, requiresAuth = true } = options;

  // Build request URL
  const url = `${API_BASE_URL}${endpoint}`;

  // Set up headers
  const requestHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    ...headers,
  };

  // Add authorization header if required
  if (requiresAuth) {
    const token = getCookie(ACCESS_TOKEN_COOKIE);
    if (!token) {
      throw new Error('Authentication required but no token found');
    }
    requestHeaders['Authorization'] = `Bearer ${token}`;
  }

  // Build request options
  const requestOptions: RequestInit = {
    method,
    headers: requestHeaders,
    credentials: 'include',
  };

  // Add body for non-GET requests
  if (method !== 'GET' && body) {
    requestOptions.body = JSON.stringify(body);
  }

  try {
    // Make the request
    const response = await fetch(url, requestOptions);

    // Handle 401 Unauthorized - attempt to refresh token and retry
    if (response.status === 401) {
      try {
        // Try to refresh the token
        await refreshAccessToken();

        // Update the authorization header with the new token
        const newToken = getCookie(ACCESS_TOKEN_COOKIE);
        if (newToken) {
          requestHeaders['Authorization'] = `Bearer ${newToken}`;

          // Retry the request with the new token
          const retryResponse = await fetch(url, {
            ...requestOptions,
            headers: requestHeaders,
          });

          if (!retryResponse.ok) {
            throw new Error(`API error: ${retryResponse.status}`);
          }

          return await retryResponse.json();
        } else {
          throw new Error('Failed to refresh authentication token');
        }
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        throw new Error('Authentication failed');
      }
    }

    // Handle other error responses
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `API error: ${response.status} ${response.statusText}`);
    }

    // Parse and return the response
    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}
