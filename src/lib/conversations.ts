export interface ConversationMessage {
  id: string;
  session_id: string;
  user_id: string;
  sender_type: 'demand' | 'assistant';
  sender_id: string;
  message_id: string;
  message_type: 'text_req' | 'text_resp' | 'escalation_req' | 'action_req' | 'bubbles';
  payload: string;
  meta: string;
  created_at: string;
  is_archived: boolean;
}

export interface ConversationsResponse {
  code: number;
  data: {
    conversations: ConversationMessage[];
    total_count: number;
  };
  message: string;
  status: string;
}

export interface ParsedConversationMessage {
  id: string;
  type: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  payload?: any;
  meta?: any;
}

/**
 * Fetch conversations for a given session ID
 */
export async function fetchConversations(sessionId: string): Promise<ConversationMessage[]> {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SERVICE_BASE_URL}/conversation-svc/v1/conversations?session_id=${sessionId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch conversations: ${response.status}`);
    }

    const data: ConversationsResponse = await response.json();
    
    if (data.code !== 200 || data.status !== 'success') {
      throw new Error(`API error: ${data.message}`);
    }

    return data.data.conversations;
  } catch (error) {
    console.error('Error fetching conversations:', error);
    throw error;
  }
}

/**
 * Parse conversation messages for display in chat interface
 */
export function parseConversationMessages(conversations: ConversationMessage[]): ParsedConversationMessage[] {
  return conversations
    .map((conv) => {
      try {
        let parsedPayload = {};
        if (conv.payload && conv.payload !== '{}') {
          parsedPayload = JSON.parse(conv.payload);
        }

        let parsedMeta = {};
        if (conv.meta && conv.meta !== '{}') {
          parsedMeta = JSON.parse(conv.meta);
        }

        let content = '';
        if (parsedPayload && typeof parsedPayload === 'object' && 'message' in parsedPayload) {
          content = (parsedPayload as any).message;
        }

        if (!content && conv.message_type === 'escalation_req') {
          return null;
        }

        const role: 'user' | 'assistant' = conv.sender_type === 'demand' ? 'user' : 'assistant';

        return {
          id: conv.id,
          type: conv.message_type,
          role,
          content,
          timestamp: new Date(conv.created_at),
          payload: parsedPayload,
          meta: parsedMeta,
        };
      } catch (error) {
        console.error('Error parsing conversation message:', conv, error);
        return null;
      }
    })
    .filter((msg): msg is ParsedConversationMessage => msg !== null)
    .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime()); // Sort by timestamp
}
