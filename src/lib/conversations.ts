export interface ConversationMessage {
  id: string;
  session_id: string;
  user_id: string;
  sender_type: 'demand' | 'assistant';
  sender_id: string;
  message_id: string;
  message_type:
    | 'text_req'
    | 'text_resp'
    | 'escalation_req'
    | 'action_req'
    | 'bubbles'
    | 'session_start_resp';
  payload: string;
  meta: string;
  created_at: string;
  is_archived: boolean;
}

export interface ConversationsResponse {
  code: number;
  data: {
    conversations: ConversationMessage[];
    total_count: number;
  };
  message: string;
  status: string;
}

export interface ParsedConversationMessage {
  id: string;
  type: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  payload?: Record<string, unknown>;
  meta?: Record<string, unknown>;
}

export interface ConversationPaginationOptions {
  page?: number;
  pageSize?: number;
}

export interface ConversationFetchResult {
  conversations: ConversationMessage[];
  hasMore: boolean;
  totalCount: number;
  currentPage: number;
}

/**
 * Fetch conversations for a given session ID with pagination support
 */
export async function fetchConversations(
  sessionId: string,
  options: ConversationPaginationOptions = {}
): Promise<ConversationFetchResult> {
  try {
    const { page = 1, pageSize = 10 } = options;

    const url = new URL(
      `${process.env.NEXT_PUBLIC_SERVICE_BASE_URL}/conversation-svc/v1/conversations`
    );
    url.searchParams.set('session_id', sessionId);
    url.searchParams.set('page', page.toString());
    url.searchParams.set('pageSize', pageSize.toString());

    console.log('Fetching conversations:', url.toString());

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch conversations: ${response.status}`);
    }

    const data: ConversationsResponse = await response.json();

    if (data.code !== 200 || data.status !== 'success') {
      throw new Error(`API error: ${data.message}`);
    }

    const conversations = data.data.conversations.filter((conversation) => {
      const payload = JSON.parse(conversation.payload);
      return (
        payload.message &&
        payload.message.trim() !== '' &&
        conversation.message_type !== 'session_start_resp'
      );
    });
    const totalCount = data.data.total_count;

    const totalPages = Math.ceil(totalCount / pageSize);
    const hasMore = page < totalPages;

    console.log(`Loaded page ${page}, ${conversations.length} conversations, hasMore: ${hasMore}`);

    return {
      conversations,
      hasMore,
      totalCount,
      currentPage: page,
    };
  } catch (error) {
    console.error('Error fetching conversations:', error);
    throw error;
  }
}
/**
 * Parse conversation messages for display in chat interface
 */
export function parseConversationMessages(
  conversations: ConversationMessage[]
): ParsedConversationMessage[] {
  return conversations
    .map((conv): ParsedConversationMessage | null => {
      try {
        let parsedPayload = {};
        if (conv.payload && conv.payload !== '{}') {
          parsedPayload = JSON.parse(conv.payload);
        }

        let parsedMeta = {};
        if (conv.meta && conv.meta !== '{}') {
          parsedMeta = JSON.parse(conv.meta);
        }

        const content = (parsedPayload as { message: string }).message;

        const role: 'user' | 'assistant' = conv.sender_type === 'demand' ? 'user' : 'assistant';

        return {
          id: conv.id,
          type: conv.message_type,
          role,
          content,
          timestamp: new Date(conv.created_at),
          payload: parsedPayload,
          meta: parsedMeta,
        } as ParsedConversationMessage;
      } catch (error) {
        console.error('Error parsing conversation message:', conv, error);
        return null;
      }
    })
    .filter((msg): msg is ParsedConversationMessage => msg !== null)
    .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
}
