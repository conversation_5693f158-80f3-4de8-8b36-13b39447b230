/* eslint-disable @typescript-eslint/no-explicit-any */
import { apiClient } from '@/lib/api-client';

// Types for brand data
export interface BrandMember {
  member: {
    id: string;
    userId: string;
    demandCode: string;
    status: string;
    metadata: {
      email?: string;
      phone?: string;
      [key: string]: any;
    };
    isArchived: boolean;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy: string;
  };
  associationType: string;
  isAuthorizedSignatory: boolean;
  metadata: {
    department?: string;
    position?: string;
    [key: string]: any;
  };
}

export interface Brand {
  id: string;
  businessName: string;
  brandCode: string;
  demandCode: string;
  description: string;
  website: string;
  metadata: {
    industry?: string;
    [key: string]: any;
  };
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface BrandWithMembersResponse {
  code: number;
  data: {
    brand: Brand;
    members: BrandMember[];
  };
  message: string;
  status: string;
}

/**
 * Fetch brand details with members for a specific customer
 * @param customerId The ID of the customer
 * @returns Brand details with members
 */
export async function getBrandWithMembers(customerId: string): Promise<BrandWithMembersResponse> {
  try {
    return await apiClient<BrandWithMembersResponse>(
      `/agent-svc/v1/brands/customer/${customerId}?demandCode=NOON`
    );
  } catch (error) {
    console.error('Failed to fetch brand with members:', error);
    throw error;
  }
}
